{"css.validate": false, "less.validate": false, "scss.validate": false, "css.customData": [{"version": 1.1, "atDirectives": [{"name": "@tailwind", "description": "Use the @tailwind directive to insert Tailwind's base, components, utilities and variants styles into your CSS."}, {"name": "@apply", "description": "Use the @apply directive to inline any existing utility classes into your own custom CSS."}, {"name": "@responsive", "description": "You can generate responsive variants of your own classes by wrapping their definitions in the @responsive directive"}, {"name": "@screen", "description": "The @screen directive allows you to create media queries that reference your breakpoints by name instead of duplicating their values in your own CSS."}, {"name": "@variants", "description": "Generate variants for your own utilities by wrapping their definitions in the @variants directive"}, {"name": "@layer", "description": "Use the @layer directive to tell Tailwind which 'bucket' a set of custom styles belong to."}, {"name": "@theme", "description": "Tailwind CSS v4 theme configuration directive"}, {"name": "@custom-variant", "description": "Tailwind CSS v4 custom variant directive"}, {"name": "@source", "description": "Tailwind CSS v4 source directive"}]}]}