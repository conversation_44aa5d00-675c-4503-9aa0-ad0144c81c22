function _0(a,o){for(var c=0;c<o.length;c++){const r=o[c];if(typeof r!="string"&&!Array.isArray(r)){for(const s in r)if(s!=="default"&&!(s in a)){const d=Object.getOwnPropertyDescriptor(r,s);d&&Object.defineProperty(a,s,d.get?d:{enumerable:!0,get:()=>r[s]})}}}return Object.freeze(Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}))}(function(){const o=document.createElement("link").relList;if(o&&o.supports&&o.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const d of s)if(d.type==="childList")for(const v of d.addedNodes)v.tagName==="LINK"&&v.rel==="modulepreload"&&r(v)}).observe(document,{childList:!0,subtree:!0});function c(s){const d={};return s.integrity&&(d.integrity=s.integrity),s.referrerPolicy&&(d.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?d.credentials="include":s.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function r(s){if(s.ep)return;s.ep=!0;const d=c(s);fetch(s.href,d)}})();function tv(a){return a&&a.__esModule&&Object.prototype.hasOwnProperty.call(a,"default")?a.default:a}var Rc={exports:{}},fu={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var mh;function C0(){if(mh)return fu;mh=1;var a=Symbol.for("react.transitional.element"),o=Symbol.for("react.fragment");function c(r,s,d){var v=null;if(d!==void 0&&(v=""+d),s.key!==void 0&&(v=""+s.key),"key"in s){d={};for(var g in s)g!=="key"&&(d[g]=s[g])}else d=s;return s=d.ref,{$$typeof:a,type:r,key:v,ref:s!==void 0?s:null,props:d}}return fu.Fragment=o,fu.jsx=c,fu.jsxs=c,fu}var hh;function D0(){return hh||(hh=1,Rc.exports=C0()),Rc.exports}var H=D0(),Oc={exports:{}},me={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var vh;function N0(){if(vh)return me;vh=1;var a=Symbol.for("react.transitional.element"),o=Symbol.for("react.portal"),c=Symbol.for("react.fragment"),r=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),d=Symbol.for("react.consumer"),v=Symbol.for("react.context"),g=Symbol.for("react.forward_ref"),y=Symbol.for("react.suspense"),h=Symbol.for("react.memo"),p=Symbol.for("react.lazy"),x=Symbol.iterator;function w(E){return E===null||typeof E!="object"?null:(E=x&&E[x]||E["@@iterator"],typeof E=="function"?E:null)}var C={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},z=Object.assign,T={};function U(E,G,$){this.props=E,this.context=G,this.refs=T,this.updater=$||C}U.prototype.isReactComponent={},U.prototype.setState=function(E,G){if(typeof E!="object"&&typeof E!="function"&&E!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,E,G,"setState")},U.prototype.forceUpdate=function(E){this.updater.enqueueForceUpdate(this,E,"forceUpdate")};function Y(){}Y.prototype=U.prototype;function J(E,G,$){this.props=E,this.context=G,this.refs=T,this.updater=$||C}var k=J.prototype=new Y;k.constructor=J,z(k,U.prototype),k.isPureReactComponent=!0;var V=Array.isArray,X={H:null,A:null,T:null,S:null,V:null},I=Object.prototype.hasOwnProperty;function F(E,G,$,K,P,fe){return $=fe.ref,{$$typeof:a,type:E,key:G,ref:$!==void 0?$:null,props:fe}}function Z(E,G){return F(E.type,G,void 0,void 0,void 0,E.props)}function re(E){return typeof E=="object"&&E!==null&&E.$$typeof===a}function he(E){var G={"=":"=0",":":"=2"};return"$"+E.replace(/[=:]/g,function($){return G[$]})}var Ee=/\/+/g;function se(E,G){return typeof E=="object"&&E!==null&&E.key!=null?he(""+E.key):G.toString(36)}function xe(){}function pe(E){switch(E.status){case"fulfilled":return E.value;case"rejected":throw E.reason;default:switch(typeof E.status=="string"?E.then(xe,xe):(E.status="pending",E.then(function(G){E.status==="pending"&&(E.status="fulfilled",E.value=G)},function(G){E.status==="pending"&&(E.status="rejected",E.reason=G)})),E.status){case"fulfilled":return E.value;case"rejected":throw E.reason}}throw E}function de(E,G,$,K,P){var fe=typeof E;(fe==="undefined"||fe==="boolean")&&(E=null);var ae=!1;if(E===null)ae=!0;else switch(fe){case"bigint":case"string":case"number":ae=!0;break;case"object":switch(E.$$typeof){case a:case o:ae=!0;break;case p:return ae=E._init,de(ae(E._payload),G,$,K,P)}}if(ae)return P=P(E),ae=K===""?"."+se(E,0):K,V(P)?($="",ae!=null&&($=ae.replace(Ee,"$&/")+"/"),de(P,G,$,"",function(nt){return nt})):P!=null&&(re(P)&&(P=Z(P,$+(P.key==null||E&&E.key===P.key?"":(""+P.key).replace(Ee,"$&/")+"/")+ae)),G.push(P)),1;ae=0;var ue=K===""?".":K+":";if(V(E))for(var Me=0;Me<E.length;Me++)K=E[Me],fe=ue+se(K,Me),ae+=de(K,G,$,fe,P);else if(Me=w(E),typeof Me=="function")for(E=Me.call(E),Me=0;!(K=E.next()).done;)K=K.value,fe=ue+se(K,Me++),ae+=de(K,G,$,fe,P);else if(fe==="object"){if(typeof E.then=="function")return de(pe(E),G,$,K,P);throw G=String(E),Error("Objects are not valid as a React child (found: "+(G==="[object Object]"?"object with keys {"+Object.keys(E).join(", ")+"}":G)+"). If you meant to render a collection of children, use an array instead.")}return ae}function O(E,G,$){if(E==null)return E;var K=[],P=0;return de(E,K,"","",function(fe){return G.call($,fe,P++)}),K}function Q(E){if(E._status===-1){var G=E._result;G=G(),G.then(function($){(E._status===0||E._status===-1)&&(E._status=1,E._result=$)},function($){(E._status===0||E._status===-1)&&(E._status=2,E._result=$)}),E._status===-1&&(E._status=0,E._result=G)}if(E._status===1)return E._result.default;throw E._result}var j=typeof reportError=="function"?reportError:function(E){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var G=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof E=="object"&&E!==null&&typeof E.message=="string"?String(E.message):String(E),error:E});if(!window.dispatchEvent(G))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",E);return}console.error(E)};function W(){}return me.Children={map:O,forEach:function(E,G,$){O(E,function(){G.apply(this,arguments)},$)},count:function(E){var G=0;return O(E,function(){G++}),G},toArray:function(E){return O(E,function(G){return G})||[]},only:function(E){if(!re(E))throw Error("React.Children.only expected to receive a single React element child.");return E}},me.Component=U,me.Fragment=c,me.Profiler=s,me.PureComponent=J,me.StrictMode=r,me.Suspense=y,me.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=X,me.__COMPILER_RUNTIME={__proto__:null,c:function(E){return X.H.useMemoCache(E)}},me.cache=function(E){return function(){return E.apply(null,arguments)}},me.cloneElement=function(E,G,$){if(E==null)throw Error("The argument must be a React element, but you passed "+E+".");var K=z({},E.props),P=E.key,fe=void 0;if(G!=null)for(ae in G.ref!==void 0&&(fe=void 0),G.key!==void 0&&(P=""+G.key),G)!I.call(G,ae)||ae==="key"||ae==="__self"||ae==="__source"||ae==="ref"&&G.ref===void 0||(K[ae]=G[ae]);var ae=arguments.length-2;if(ae===1)K.children=$;else if(1<ae){for(var ue=Array(ae),Me=0;Me<ae;Me++)ue[Me]=arguments[Me+2];K.children=ue}return F(E.type,P,void 0,void 0,fe,K)},me.createContext=function(E){return E={$$typeof:v,_currentValue:E,_currentValue2:E,_threadCount:0,Provider:null,Consumer:null},E.Provider=E,E.Consumer={$$typeof:d,_context:E},E},me.createElement=function(E,G,$){var K,P={},fe=null;if(G!=null)for(K in G.key!==void 0&&(fe=""+G.key),G)I.call(G,K)&&K!=="key"&&K!=="__self"&&K!=="__source"&&(P[K]=G[K]);var ae=arguments.length-2;if(ae===1)P.children=$;else if(1<ae){for(var ue=Array(ae),Me=0;Me<ae;Me++)ue[Me]=arguments[Me+2];P.children=ue}if(E&&E.defaultProps)for(K in ae=E.defaultProps,ae)P[K]===void 0&&(P[K]=ae[K]);return F(E,fe,void 0,void 0,null,P)},me.createRef=function(){return{current:null}},me.forwardRef=function(E){return{$$typeof:g,render:E}},me.isValidElement=re,me.lazy=function(E){return{$$typeof:p,_payload:{_status:-1,_result:E},_init:Q}},me.memo=function(E,G){return{$$typeof:h,type:E,compare:G===void 0?null:G}},me.startTransition=function(E){var G=X.T,$={};X.T=$;try{var K=E(),P=X.S;P!==null&&P($,K),typeof K=="object"&&K!==null&&typeof K.then=="function"&&K.then(W,j)}catch(fe){j(fe)}finally{X.T=G}},me.unstable_useCacheRefresh=function(){return X.H.useCacheRefresh()},me.use=function(E){return X.H.use(E)},me.useActionState=function(E,G,$){return X.H.useActionState(E,G,$)},me.useCallback=function(E,G){return X.H.useCallback(E,G)},me.useContext=function(E){return X.H.useContext(E)},me.useDebugValue=function(){},me.useDeferredValue=function(E,G){return X.H.useDeferredValue(E,G)},me.useEffect=function(E,G,$){var K=X.H;if(typeof $=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return K.useEffect(E,G)},me.useId=function(){return X.H.useId()},me.useImperativeHandle=function(E,G,$){return X.H.useImperativeHandle(E,G,$)},me.useInsertionEffect=function(E,G){return X.H.useInsertionEffect(E,G)},me.useLayoutEffect=function(E,G){return X.H.useLayoutEffect(E,G)},me.useMemo=function(E,G){return X.H.useMemo(E,G)},me.useOptimistic=function(E,G){return X.H.useOptimistic(E,G)},me.useReducer=function(E,G,$){return X.H.useReducer(E,G,$)},me.useRef=function(E){return X.H.useRef(E)},me.useState=function(E){return X.H.useState(E)},me.useSyncExternalStore=function(E,G,$){return X.H.useSyncExternalStore(E,G,$)},me.useTransition=function(){return X.H.useTransition()},me.version="19.1.1",me}var gh;function ns(){return gh||(gh=1,Oc.exports=N0()),Oc.exports}var b=ns();const Qn=tv(b),nv=_0({__proto__:null,default:Qn},[b]);var _c={exports:{}},du={},Cc={exports:{}},Dc={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ph;function z0(){return ph||(ph=1,(function(a){function o(O,Q){var j=O.length;O.push(Q);e:for(;0<j;){var W=j-1>>>1,E=O[W];if(0<s(E,Q))O[W]=Q,O[j]=E,j=W;else break e}}function c(O){return O.length===0?null:O[0]}function r(O){if(O.length===0)return null;var Q=O[0],j=O.pop();if(j!==Q){O[0]=j;e:for(var W=0,E=O.length,G=E>>>1;W<G;){var $=2*(W+1)-1,K=O[$],P=$+1,fe=O[P];if(0>s(K,j))P<E&&0>s(fe,K)?(O[W]=fe,O[P]=j,W=P):(O[W]=K,O[$]=j,W=$);else if(P<E&&0>s(fe,j))O[W]=fe,O[P]=j,W=P;else break e}}return Q}function s(O,Q){var j=O.sortIndex-Q.sortIndex;return j!==0?j:O.id-Q.id}if(a.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var d=performance;a.unstable_now=function(){return d.now()}}else{var v=Date,g=v.now();a.unstable_now=function(){return v.now()-g}}var y=[],h=[],p=1,x=null,w=3,C=!1,z=!1,T=!1,U=!1,Y=typeof setTimeout=="function"?setTimeout:null,J=typeof clearTimeout=="function"?clearTimeout:null,k=typeof setImmediate<"u"?setImmediate:null;function V(O){for(var Q=c(h);Q!==null;){if(Q.callback===null)r(h);else if(Q.startTime<=O)r(h),Q.sortIndex=Q.expirationTime,o(y,Q);else break;Q=c(h)}}function X(O){if(T=!1,V(O),!z)if(c(y)!==null)z=!0,I||(I=!0,se());else{var Q=c(h);Q!==null&&de(X,Q.startTime-O)}}var I=!1,F=-1,Z=5,re=-1;function he(){return U?!0:!(a.unstable_now()-re<Z)}function Ee(){if(U=!1,I){var O=a.unstable_now();re=O;var Q=!0;try{e:{z=!1,T&&(T=!1,J(F),F=-1),C=!0;var j=w;try{t:{for(V(O),x=c(y);x!==null&&!(x.expirationTime>O&&he());){var W=x.callback;if(typeof W=="function"){x.callback=null,w=x.priorityLevel;var E=W(x.expirationTime<=O);if(O=a.unstable_now(),typeof E=="function"){x.callback=E,V(O),Q=!0;break t}x===c(y)&&r(y),V(O)}else r(y);x=c(y)}if(x!==null)Q=!0;else{var G=c(h);G!==null&&de(X,G.startTime-O),Q=!1}}break e}finally{x=null,w=j,C=!1}Q=void 0}}finally{Q?se():I=!1}}}var se;if(typeof k=="function")se=function(){k(Ee)};else if(typeof MessageChannel<"u"){var xe=new MessageChannel,pe=xe.port2;xe.port1.onmessage=Ee,se=function(){pe.postMessage(null)}}else se=function(){Y(Ee,0)};function de(O,Q){F=Y(function(){O(a.unstable_now())},Q)}a.unstable_IdlePriority=5,a.unstable_ImmediatePriority=1,a.unstable_LowPriority=4,a.unstable_NormalPriority=3,a.unstable_Profiling=null,a.unstable_UserBlockingPriority=2,a.unstable_cancelCallback=function(O){O.callback=null},a.unstable_forceFrameRate=function(O){0>O||125<O?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):Z=0<O?Math.floor(1e3/O):5},a.unstable_getCurrentPriorityLevel=function(){return w},a.unstable_next=function(O){switch(w){case 1:case 2:case 3:var Q=3;break;default:Q=w}var j=w;w=Q;try{return O()}finally{w=j}},a.unstable_requestPaint=function(){U=!0},a.unstable_runWithPriority=function(O,Q){switch(O){case 1:case 2:case 3:case 4:case 5:break;default:O=3}var j=w;w=O;try{return Q()}finally{w=j}},a.unstable_scheduleCallback=function(O,Q,j){var W=a.unstable_now();switch(typeof j=="object"&&j!==null?(j=j.delay,j=typeof j=="number"&&0<j?W+j:W):j=W,O){case 1:var E=-1;break;case 2:E=250;break;case 5:E=1073741823;break;case 4:E=1e4;break;default:E=5e3}return E=j+E,O={id:p++,callback:Q,priorityLevel:O,startTime:j,expirationTime:E,sortIndex:-1},j>W?(O.sortIndex=j,o(h,O),c(y)===null&&O===c(h)&&(T?(J(F),F=-1):T=!0,de(X,j-W))):(O.sortIndex=E,o(y,O),z||C||(z=!0,I||(I=!0,se()))),O},a.unstable_shouldYield=he,a.unstable_wrapCallback=function(O){var Q=w;return function(){var j=w;w=Q;try{return O.apply(this,arguments)}finally{w=j}}}})(Dc)),Dc}var yh;function U0(){return yh||(yh=1,Cc.exports=z0()),Cc.exports}var Nc={exports:{}},tt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var bh;function j0(){if(bh)return tt;bh=1;var a=ns();function o(y){var h="https://react.dev/errors/"+y;if(1<arguments.length){h+="?args[]="+encodeURIComponent(arguments[1]);for(var p=2;p<arguments.length;p++)h+="&args[]="+encodeURIComponent(arguments[p])}return"Minified React error #"+y+"; visit "+h+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function c(){}var r={d:{f:c,r:function(){throw Error(o(522))},D:c,C:c,L:c,m:c,X:c,S:c,M:c},p:0,findDOMNode:null},s=Symbol.for("react.portal");function d(y,h,p){var x=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:s,key:x==null?null:""+x,children:y,containerInfo:h,implementation:p}}var v=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function g(y,h){if(y==="font")return"";if(typeof h=="string")return h==="use-credentials"?h:""}return tt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=r,tt.createPortal=function(y,h){var p=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!h||h.nodeType!==1&&h.nodeType!==9&&h.nodeType!==11)throw Error(o(299));return d(y,h,null,p)},tt.flushSync=function(y){var h=v.T,p=r.p;try{if(v.T=null,r.p=2,y)return y()}finally{v.T=h,r.p=p,r.d.f()}},tt.preconnect=function(y,h){typeof y=="string"&&(h?(h=h.crossOrigin,h=typeof h=="string"?h==="use-credentials"?h:"":void 0):h=null,r.d.C(y,h))},tt.prefetchDNS=function(y){typeof y=="string"&&r.d.D(y)},tt.preinit=function(y,h){if(typeof y=="string"&&h&&typeof h.as=="string"){var p=h.as,x=g(p,h.crossOrigin),w=typeof h.integrity=="string"?h.integrity:void 0,C=typeof h.fetchPriority=="string"?h.fetchPriority:void 0;p==="style"?r.d.S(y,typeof h.precedence=="string"?h.precedence:void 0,{crossOrigin:x,integrity:w,fetchPriority:C}):p==="script"&&r.d.X(y,{crossOrigin:x,integrity:w,fetchPriority:C,nonce:typeof h.nonce=="string"?h.nonce:void 0})}},tt.preinitModule=function(y,h){if(typeof y=="string")if(typeof h=="object"&&h!==null){if(h.as==null||h.as==="script"){var p=g(h.as,h.crossOrigin);r.d.M(y,{crossOrigin:p,integrity:typeof h.integrity=="string"?h.integrity:void 0,nonce:typeof h.nonce=="string"?h.nonce:void 0})}}else h==null&&r.d.M(y)},tt.preload=function(y,h){if(typeof y=="string"&&typeof h=="object"&&h!==null&&typeof h.as=="string"){var p=h.as,x=g(p,h.crossOrigin);r.d.L(y,p,{crossOrigin:x,integrity:typeof h.integrity=="string"?h.integrity:void 0,nonce:typeof h.nonce=="string"?h.nonce:void 0,type:typeof h.type=="string"?h.type:void 0,fetchPriority:typeof h.fetchPriority=="string"?h.fetchPriority:void 0,referrerPolicy:typeof h.referrerPolicy=="string"?h.referrerPolicy:void 0,imageSrcSet:typeof h.imageSrcSet=="string"?h.imageSrcSet:void 0,imageSizes:typeof h.imageSizes=="string"?h.imageSizes:void 0,media:typeof h.media=="string"?h.media:void 0})}},tt.preloadModule=function(y,h){if(typeof y=="string")if(h){var p=g(h.as,h.crossOrigin);r.d.m(y,{as:typeof h.as=="string"&&h.as!=="script"?h.as:void 0,crossOrigin:p,integrity:typeof h.integrity=="string"?h.integrity:void 0})}else r.d.m(y)},tt.requestFormReset=function(y){r.d.r(y)},tt.unstable_batchedUpdates=function(y,h){return y(h)},tt.useFormState=function(y,h,p){return v.H.useFormState(y,h,p)},tt.useFormStatus=function(){return v.H.useHostTransitionStatus()},tt.version="19.1.1",tt}var Sh;function lv(){if(Sh)return Nc.exports;Sh=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(o){console.error(o)}}return a(),Nc.exports=j0(),Nc.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var xh;function H0(){if(xh)return du;xh=1;var a=U0(),o=ns(),c=lv();function r(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function d(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function v(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function g(e){if(d(e)!==e)throw Error(r(188))}function y(e){var t=e.alternate;if(!t){if(t=d(e),t===null)throw Error(r(188));return t!==e?null:e}for(var n=e,l=t;;){var u=n.return;if(u===null)break;var i=u.alternate;if(i===null){if(l=u.return,l!==null){n=l;continue}break}if(u.child===i.child){for(i=u.child;i;){if(i===n)return g(u),e;if(i===l)return g(u),t;i=i.sibling}throw Error(r(188))}if(n.return!==l.return)n=u,l=i;else{for(var f=!1,m=u.child;m;){if(m===n){f=!0,n=u,l=i;break}if(m===l){f=!0,l=u,n=i;break}m=m.sibling}if(!f){for(m=i.child;m;){if(m===n){f=!0,n=i,l=u;break}if(m===l){f=!0,l=i,n=u;break}m=m.sibling}if(!f)throw Error(r(189))}}if(n.alternate!==l)throw Error(r(190))}if(n.tag!==3)throw Error(r(188));return n.stateNode.current===n?e:t}function h(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=h(e),t!==null)return t;e=e.sibling}return null}var p=Object.assign,x=Symbol.for("react.element"),w=Symbol.for("react.transitional.element"),C=Symbol.for("react.portal"),z=Symbol.for("react.fragment"),T=Symbol.for("react.strict_mode"),U=Symbol.for("react.profiler"),Y=Symbol.for("react.provider"),J=Symbol.for("react.consumer"),k=Symbol.for("react.context"),V=Symbol.for("react.forward_ref"),X=Symbol.for("react.suspense"),I=Symbol.for("react.suspense_list"),F=Symbol.for("react.memo"),Z=Symbol.for("react.lazy"),re=Symbol.for("react.activity"),he=Symbol.for("react.memo_cache_sentinel"),Ee=Symbol.iterator;function se(e){return e===null||typeof e!="object"?null:(e=Ee&&e[Ee]||e["@@iterator"],typeof e=="function"?e:null)}var xe=Symbol.for("react.client.reference");function pe(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===xe?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case z:return"Fragment";case U:return"Profiler";case T:return"StrictMode";case X:return"Suspense";case I:return"SuspenseList";case re:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case C:return"Portal";case k:return(e.displayName||"Context")+".Provider";case J:return(e._context.displayName||"Context")+".Consumer";case V:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case F:return t=e.displayName||null,t!==null?t:pe(e.type)||"Memo";case Z:t=e._payload,e=e._init;try{return pe(e(t))}catch{}}return null}var de=Array.isArray,O=o.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Q=c.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,j={pending:!1,data:null,method:null,action:null},W=[],E=-1;function G(e){return{current:e}}function $(e){0>E||(e.current=W[E],W[E]=null,E--)}function K(e,t){E++,W[E]=e.current,e.current=t}var P=G(null),fe=G(null),ae=G(null),ue=G(null);function Me(e,t){switch(K(ae,t),K(fe,e),K(P,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?qm(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=qm(t),e=km(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}$(P),K(P,e)}function nt(){$(P),$(fe),$(ae)}function Lt(e){e.memoizedState!==null&&K(ue,e);var t=P.current,n=km(t,e.type);t!==n&&(K(fe,e),K(P,n))}function Gt(e){fe.current===e&&($(P),$(fe)),ue.current===e&&($(ue),iu._currentValue=j)}var Yt=Object.prototype.hasOwnProperty,bn=a.unstable_scheduleCallback,vo=a.unstable_cancelCallback,op=a.unstable_shouldYield,rp=a.unstable_requestPaint,qt=a.unstable_now,cp=a.unstable_getCurrentPriorityLevel,Ss=a.unstable_ImmediatePriority,xs=a.unstable_UserBlockingPriority,Ru=a.unstable_NormalPriority,sp=a.unstable_LowPriority,Es=a.unstable_IdlePriority,fp=a.log,dp=a.unstable_setDisableYieldValue,ha=null,dt=null;function Sn(e){if(typeof fp=="function"&&dp(e),dt&&typeof dt.setStrictMode=="function")try{dt.setStrictMode(ha,e)}catch{}}var mt=Math.clz32?Math.clz32:vp,mp=Math.log,hp=Math.LN2;function vp(e){return e>>>=0,e===0?32:31-(mp(e)/hp|0)|0}var Ou=256,_u=4194304;function Fn(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function Cu(e,t,n){var l=e.pendingLanes;if(l===0)return 0;var u=0,i=e.suspendedLanes,f=e.pingedLanes;e=e.warmLanes;var m=l&134217727;return m!==0?(l=m&~i,l!==0?u=Fn(l):(f&=m,f!==0?u=Fn(f):n||(n=m&~e,n!==0&&(u=Fn(n))))):(m=l&~i,m!==0?u=Fn(m):f!==0?u=Fn(f):n||(n=l&~e,n!==0&&(u=Fn(n)))),u===0?0:t!==0&&t!==u&&(t&i)===0&&(i=u&-u,n=t&-t,i>=n||i===32&&(n&4194048)!==0)?t:u}function va(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function gp(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function As(){var e=Ou;return Ou<<=1,(Ou&4194048)===0&&(Ou=256),e}function ws(){var e=_u;return _u<<=1,(_u&62914560)===0&&(_u=4194304),e}function go(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function ga(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function pp(e,t,n,l,u,i){var f=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var m=e.entanglements,S=e.expirationTimes,_=e.hiddenUpdates;for(n=f&~n;0<n;){var B=31-mt(n),q=1<<B;m[B]=0,S[B]=-1;var D=_[B];if(D!==null)for(_[B]=null,B=0;B<D.length;B++){var N=D[B];N!==null&&(N.lane&=-536870913)}n&=~q}l!==0&&Ms(e,l,0),i!==0&&u===0&&e.tag!==0&&(e.suspendedLanes|=i&~(f&~t))}function Ms(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var l=31-mt(t);e.entangledLanes|=t,e.entanglements[l]=e.entanglements[l]|1073741824|n&4194090}function Ts(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var l=31-mt(n),u=1<<l;u&t|e[l]&t&&(e[l]|=t),n&=~u}}function po(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function yo(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Rs(){var e=Q.p;return e!==0?e:(e=window.event,e===void 0?32:oh(e.type))}function yp(e,t){var n=Q.p;try{return Q.p=e,t()}finally{Q.p=n}}var xn=Math.random().toString(36).slice(2),Ie="__reactFiber$"+xn,it="__reactProps$"+xn,yl="__reactContainer$"+xn,bo="__reactEvents$"+xn,bp="__reactListeners$"+xn,Sp="__reactHandles$"+xn,Os="__reactResources$"+xn,pa="__reactMarker$"+xn;function So(e){delete e[Ie],delete e[it],delete e[bo],delete e[bp],delete e[Sp]}function bl(e){var t=e[Ie];if(t)return t;for(var n=e.parentNode;n;){if(t=n[yl]||n[Ie]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Zm(e);e!==null;){if(n=e[Ie])return n;e=Zm(e)}return t}e=n,n=e.parentNode}return null}function Sl(e){if(e=e[Ie]||e[yl]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function ya(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(r(33))}function xl(e){var t=e[Os];return t||(t=e[Os]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Ze(e){e[pa]=!0}var _s=new Set,Cs={};function $n(e,t){El(e,t),El(e+"Capture",t)}function El(e,t){for(Cs[e]=t,e=0;e<t.length;e++)_s.add(t[e])}var xp=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Ds={},Ns={};function Ep(e){return Yt.call(Ns,e)?!0:Yt.call(Ds,e)?!1:xp.test(e)?Ns[e]=!0:(Ds[e]=!0,!1)}function Du(e,t,n){if(Ep(t))if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var l=t.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+n)}}function Nu(e,t,n){if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+n)}}function Pt(e,t,n,l){if(l===null)e.removeAttribute(n);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(n);return}e.setAttributeNS(t,n,""+l)}}var xo,zs;function Al(e){if(xo===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);xo=t&&t[1]||"",zs=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+xo+e+zs}var Eo=!1;function Ao(e,t){if(!e||Eo)return"";Eo=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(t){var q=function(){throw Error()};if(Object.defineProperty(q.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(q,[])}catch(N){var D=N}Reflect.construct(e,[],q)}else{try{q.call()}catch(N){D=N}e.call(q.prototype)}}else{try{throw Error()}catch(N){D=N}(q=e())&&typeof q.catch=="function"&&q.catch(function(){})}}catch(N){if(N&&D&&typeof N.stack=="string")return[N.stack,D.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var u=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");u&&u.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var i=l.DetermineComponentFrameRoot(),f=i[0],m=i[1];if(f&&m){var S=f.split(`
`),_=m.split(`
`);for(u=l=0;l<S.length&&!S[l].includes("DetermineComponentFrameRoot");)l++;for(;u<_.length&&!_[u].includes("DetermineComponentFrameRoot");)u++;if(l===S.length||u===_.length)for(l=S.length-1,u=_.length-1;1<=l&&0<=u&&S[l]!==_[u];)u--;for(;1<=l&&0<=u;l--,u--)if(S[l]!==_[u]){if(l!==1||u!==1)do if(l--,u--,0>u||S[l]!==_[u]){var B=`
`+S[l].replace(" at new "," at ");return e.displayName&&B.includes("<anonymous>")&&(B=B.replace("<anonymous>",e.displayName)),B}while(1<=l&&0<=u);break}}}finally{Eo=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?Al(n):""}function Ap(e){switch(e.tag){case 26:case 27:case 5:return Al(e.type);case 16:return Al("Lazy");case 13:return Al("Suspense");case 19:return Al("SuspenseList");case 0:case 15:return Ao(e.type,!1);case 11:return Ao(e.type.render,!1);case 1:return Ao(e.type,!0);case 31:return Al("Activity");default:return""}}function Us(e){try{var t="";do t+=Ap(e),e=e.return;while(e);return t}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function At(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function js(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function wp(e){var t=js(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),l=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var u=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return u.call(this)},set:function(f){l=""+f,i.call(this,f)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return l},setValue:function(f){l=""+f},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function zu(e){e._valueTracker||(e._valueTracker=wp(e))}function Hs(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),l="";return e&&(l=js(e)?e.checked?"true":"false":e.value),e=l,e!==n?(t.setValue(e),!0):!1}function Uu(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var Mp=/[\n"\\]/g;function wt(e){return e.replace(Mp,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function wo(e,t,n,l,u,i,f,m){e.name="",f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"?e.type=f:e.removeAttribute("type"),t!=null?f==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+At(t)):e.value!==""+At(t)&&(e.value=""+At(t)):f!=="submit"&&f!=="reset"||e.removeAttribute("value"),t!=null?Mo(e,f,At(t)):n!=null?Mo(e,f,At(n)):l!=null&&e.removeAttribute("value"),u==null&&i!=null&&(e.defaultChecked=!!i),u!=null&&(e.checked=u&&typeof u!="function"&&typeof u!="symbol"),m!=null&&typeof m!="function"&&typeof m!="symbol"&&typeof m!="boolean"?e.name=""+At(m):e.removeAttribute("name")}function Bs(e,t,n,l,u,i,f,m){if(i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"&&(e.type=i),t!=null||n!=null){if(!(i!=="submit"&&i!=="reset"||t!=null))return;n=n!=null?""+At(n):"",t=t!=null?""+At(t):n,m||t===e.value||(e.value=t),e.defaultValue=t}l=l??u,l=typeof l!="function"&&typeof l!="symbol"&&!!l,e.checked=m?e.checked:!!l,e.defaultChecked=!!l,f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(e.name=f)}function Mo(e,t,n){t==="number"&&Uu(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function wl(e,t,n,l){if(e=e.options,t){t={};for(var u=0;u<n.length;u++)t["$"+n[u]]=!0;for(n=0;n<e.length;n++)u=t.hasOwnProperty("$"+e[n].value),e[n].selected!==u&&(e[n].selected=u),u&&l&&(e[n].defaultSelected=!0)}else{for(n=""+At(n),t=null,u=0;u<e.length;u++){if(e[u].value===n){e[u].selected=!0,l&&(e[u].defaultSelected=!0);return}t!==null||e[u].disabled||(t=e[u])}t!==null&&(t.selected=!0)}}function Ls(e,t,n){if(t!=null&&(t=""+At(t),t!==e.value&&(e.value=t),n==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=n!=null?""+At(n):""}function Gs(e,t,n,l){if(t==null){if(l!=null){if(n!=null)throw Error(r(92));if(de(l)){if(1<l.length)throw Error(r(93));l=l[0]}n=l}n==null&&(n=""),t=n}n=At(t),e.defaultValue=n,l=e.textContent,l===n&&l!==""&&l!==null&&(e.value=l)}function Ml(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Tp=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Ys(e,t,n){var l=t.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?l?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":l?e.setProperty(t,n):typeof n!="number"||n===0||Tp.has(t)?t==="float"?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function qs(e,t,n){if(t!=null&&typeof t!="object")throw Error(r(62));if(e=e.style,n!=null){for(var l in n)!n.hasOwnProperty(l)||t!=null&&t.hasOwnProperty(l)||(l.indexOf("--")===0?e.setProperty(l,""):l==="float"?e.cssFloat="":e[l]="");for(var u in t)l=t[u],t.hasOwnProperty(u)&&n[u]!==l&&Ys(e,u,l)}else for(var i in t)t.hasOwnProperty(i)&&Ys(e,i,t[i])}function To(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Rp=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Op=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function ju(e){return Op.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Ro=null;function Oo(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Tl=null,Rl=null;function ks(e){var t=Sl(e);if(t&&(e=t.stateNode)){var n=e[it]||null;e:switch(e=t.stateNode,t.type){case"input":if(wo(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+wt(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var l=n[t];if(l!==e&&l.form===e.form){var u=l[it]||null;if(!u)throw Error(r(90));wo(l,u.value,u.defaultValue,u.defaultValue,u.checked,u.defaultChecked,u.type,u.name)}}for(t=0;t<n.length;t++)l=n[t],l.form===e.form&&Hs(l)}break e;case"textarea":Ls(e,n.value,n.defaultValue);break e;case"select":t=n.value,t!=null&&wl(e,!!n.multiple,t,!1)}}}var _o=!1;function Xs(e,t,n){if(_o)return e(t,n);_o=!0;try{var l=e(t);return l}finally{if(_o=!1,(Tl!==null||Rl!==null)&&(Si(),Tl&&(t=Tl,e=Rl,Rl=Tl=null,ks(t),e)))for(t=0;t<e.length;t++)ks(e[t])}}function ba(e,t){var n=e.stateNode;if(n===null)return null;var l=n[it]||null;if(l===null)return null;n=l[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(e=e.type,l=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!l;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(r(231,t,typeof n));return n}var It=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Co=!1;if(It)try{var Sa={};Object.defineProperty(Sa,"passive",{get:function(){Co=!0}}),window.addEventListener("test",Sa,Sa),window.removeEventListener("test",Sa,Sa)}catch{Co=!1}var En=null,Do=null,Hu=null;function Vs(){if(Hu)return Hu;var e,t=Do,n=t.length,l,u="value"in En?En.value:En.textContent,i=u.length;for(e=0;e<n&&t[e]===u[e];e++);var f=n-e;for(l=1;l<=f&&t[n-l]===u[i-l];l++);return Hu=u.slice(e,1<l?1-l:void 0)}function Bu(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Lu(){return!0}function Qs(){return!1}function ot(e){function t(n,l,u,i,f){this._reactName=n,this._targetInst=u,this.type=l,this.nativeEvent=i,this.target=f,this.currentTarget=null;for(var m in e)e.hasOwnProperty(m)&&(n=e[m],this[m]=n?n(i):i[m]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Lu:Qs,this.isPropagationStopped=Qs,this}return p(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Lu)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Lu)},persist:function(){},isPersistent:Lu}),t}var Pn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Gu=ot(Pn),xa=p({},Pn,{view:0,detail:0}),_p=ot(xa),No,zo,Ea,Yu=p({},xa,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:jo,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Ea&&(Ea&&e.type==="mousemove"?(No=e.screenX-Ea.screenX,zo=e.screenY-Ea.screenY):zo=No=0,Ea=e),No)},movementY:function(e){return"movementY"in e?e.movementY:zo}}),Zs=ot(Yu),Cp=p({},Yu,{dataTransfer:0}),Dp=ot(Cp),Np=p({},xa,{relatedTarget:0}),Uo=ot(Np),zp=p({},Pn,{animationName:0,elapsedTime:0,pseudoElement:0}),Up=ot(zp),jp=p({},Pn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Hp=ot(jp),Bp=p({},Pn,{data:0}),Ks=ot(Bp),Lp={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Gp={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Yp={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function qp(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Yp[e])?!!t[e]:!1}function jo(){return qp}var kp=p({},xa,{key:function(e){if(e.key){var t=Lp[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Bu(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Gp[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:jo,charCode:function(e){return e.type==="keypress"?Bu(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Bu(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Xp=ot(kp),Vp=p({},Yu,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Js=ot(Vp),Qp=p({},xa,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:jo}),Zp=ot(Qp),Kp=p({},Pn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Jp=ot(Kp),Wp=p({},Yu,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Fp=ot(Wp),$p=p({},Pn,{newState:0,oldState:0}),Pp=ot($p),Ip=[9,13,27,32],Ho=It&&"CompositionEvent"in window,Aa=null;It&&"documentMode"in document&&(Aa=document.documentMode);var ey=It&&"TextEvent"in window&&!Aa,Ws=It&&(!Ho||Aa&&8<Aa&&11>=Aa),Fs=" ",$s=!1;function Ps(e,t){switch(e){case"keyup":return Ip.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Is(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Ol=!1;function ty(e,t){switch(e){case"compositionend":return Is(t);case"keypress":return t.which!==32?null:($s=!0,Fs);case"textInput":return e=t.data,e===Fs&&$s?null:e;default:return null}}function ny(e,t){if(Ol)return e==="compositionend"||!Ho&&Ps(e,t)?(e=Vs(),Hu=Do=En=null,Ol=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Ws&&t.locale!=="ko"?null:t.data;default:return null}}var ly={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function ef(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!ly[e.type]:t==="textarea"}function tf(e,t,n,l){Tl?Rl?Rl.push(l):Rl=[l]:Tl=l,t=Ti(t,"onChange"),0<t.length&&(n=new Gu("onChange","change",null,n,l),e.push({event:n,listeners:t}))}var wa=null,Ma=null;function ay(e){Hm(e,0)}function qu(e){var t=ya(e);if(Hs(t))return e}function nf(e,t){if(e==="change")return t}var lf=!1;if(It){var Bo;if(It){var Lo="oninput"in document;if(!Lo){var af=document.createElement("div");af.setAttribute("oninput","return;"),Lo=typeof af.oninput=="function"}Bo=Lo}else Bo=!1;lf=Bo&&(!document.documentMode||9<document.documentMode)}function uf(){wa&&(wa.detachEvent("onpropertychange",of),Ma=wa=null)}function of(e){if(e.propertyName==="value"&&qu(Ma)){var t=[];tf(t,Ma,e,Oo(e)),Xs(ay,t)}}function uy(e,t,n){e==="focusin"?(uf(),wa=t,Ma=n,wa.attachEvent("onpropertychange",of)):e==="focusout"&&uf()}function iy(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return qu(Ma)}function oy(e,t){if(e==="click")return qu(t)}function ry(e,t){if(e==="input"||e==="change")return qu(t)}function cy(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var ht=typeof Object.is=="function"?Object.is:cy;function Ta(e,t){if(ht(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),l=Object.keys(t);if(n.length!==l.length)return!1;for(l=0;l<n.length;l++){var u=n[l];if(!Yt.call(t,u)||!ht(e[u],t[u]))return!1}return!0}function rf(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function cf(e,t){var n=rf(e);e=0;for(var l;n;){if(n.nodeType===3){if(l=e+n.textContent.length,e<=t&&l>=t)return{node:n,offset:t-e};e=l}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=rf(n)}}function sf(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?sf(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function ff(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=Uu(e.document);t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Uu(e.document)}return t}function Go(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var sy=It&&"documentMode"in document&&11>=document.documentMode,_l=null,Yo=null,Ra=null,qo=!1;function df(e,t,n){var l=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;qo||_l==null||_l!==Uu(l)||(l=_l,"selectionStart"in l&&Go(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),Ra&&Ta(Ra,l)||(Ra=l,l=Ti(Yo,"onSelect"),0<l.length&&(t=new Gu("onSelect","select",null,t,n),e.push({event:t,listeners:l}),t.target=_l)))}function In(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Cl={animationend:In("Animation","AnimationEnd"),animationiteration:In("Animation","AnimationIteration"),animationstart:In("Animation","AnimationStart"),transitionrun:In("Transition","TransitionRun"),transitionstart:In("Transition","TransitionStart"),transitioncancel:In("Transition","TransitionCancel"),transitionend:In("Transition","TransitionEnd")},ko={},mf={};It&&(mf=document.createElement("div").style,"AnimationEvent"in window||(delete Cl.animationend.animation,delete Cl.animationiteration.animation,delete Cl.animationstart.animation),"TransitionEvent"in window||delete Cl.transitionend.transition);function el(e){if(ko[e])return ko[e];if(!Cl[e])return e;var t=Cl[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in mf)return ko[e]=t[n];return e}var hf=el("animationend"),vf=el("animationiteration"),gf=el("animationstart"),fy=el("transitionrun"),dy=el("transitionstart"),my=el("transitioncancel"),pf=el("transitionend"),yf=new Map,Xo="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Xo.push("scrollEnd");function zt(e,t){yf.set(e,t),$n(t,[e])}var bf=new WeakMap;function Mt(e,t){if(typeof e=="object"&&e!==null){var n=bf.get(e);return n!==void 0?n:(t={value:e,source:t,stack:Us(t)},bf.set(e,t),t)}return{value:e,source:t,stack:Us(t)}}var Tt=[],Dl=0,Vo=0;function ku(){for(var e=Dl,t=Vo=Dl=0;t<e;){var n=Tt[t];Tt[t++]=null;var l=Tt[t];Tt[t++]=null;var u=Tt[t];Tt[t++]=null;var i=Tt[t];if(Tt[t++]=null,l!==null&&u!==null){var f=l.pending;f===null?u.next=u:(u.next=f.next,f.next=u),l.pending=u}i!==0&&Sf(n,u,i)}}function Xu(e,t,n,l){Tt[Dl++]=e,Tt[Dl++]=t,Tt[Dl++]=n,Tt[Dl++]=l,Vo|=l,e.lanes|=l,e=e.alternate,e!==null&&(e.lanes|=l)}function Qo(e,t,n,l){return Xu(e,t,n,l),Vu(e)}function Nl(e,t){return Xu(e,null,null,t),Vu(e)}function Sf(e,t,n){e.lanes|=n;var l=e.alternate;l!==null&&(l.lanes|=n);for(var u=!1,i=e.return;i!==null;)i.childLanes|=n,l=i.alternate,l!==null&&(l.childLanes|=n),i.tag===22&&(e=i.stateNode,e===null||e._visibility&1||(u=!0)),e=i,i=i.return;return e.tag===3?(i=e.stateNode,u&&t!==null&&(u=31-mt(n),e=i.hiddenUpdates,l=e[u],l===null?e[u]=[t]:l.push(t),t.lane=n|536870912),i):null}function Vu(e){if(50<Pa)throw Pa=0,$r=null,Error(r(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var zl={};function hy(e,t,n,l){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function vt(e,t,n,l){return new hy(e,t,n,l)}function Zo(e){return e=e.prototype,!(!e||!e.isReactComponent)}function en(e,t){var n=e.alternate;return n===null?(n=vt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&65011712,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function xf(e,t){e.flags&=65011714;var n=e.alternate;return n===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Qu(e,t,n,l,u,i){var f=0;if(l=e,typeof e=="function")Zo(e)&&(f=1);else if(typeof e=="string")f=g0(e,n,P.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case re:return e=vt(31,n,t,u),e.elementType=re,e.lanes=i,e;case z:return tl(n.children,u,i,t);case T:f=8,u|=24;break;case U:return e=vt(12,n,t,u|2),e.elementType=U,e.lanes=i,e;case X:return e=vt(13,n,t,u),e.elementType=X,e.lanes=i,e;case I:return e=vt(19,n,t,u),e.elementType=I,e.lanes=i,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Y:case k:f=10;break e;case J:f=9;break e;case V:f=11;break e;case F:f=14;break e;case Z:f=16,l=null;break e}f=29,n=Error(r(130,e===null?"null":typeof e,"")),l=null}return t=vt(f,n,t,u),t.elementType=e,t.type=l,t.lanes=i,t}function tl(e,t,n,l){return e=vt(7,e,l,t),e.lanes=n,e}function Ko(e,t,n){return e=vt(6,e,null,t),e.lanes=n,e}function Jo(e,t,n){return t=vt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Ul=[],jl=0,Zu=null,Ku=0,Rt=[],Ot=0,nl=null,tn=1,nn="";function ll(e,t){Ul[jl++]=Ku,Ul[jl++]=Zu,Zu=e,Ku=t}function Ef(e,t,n){Rt[Ot++]=tn,Rt[Ot++]=nn,Rt[Ot++]=nl,nl=e;var l=tn;e=nn;var u=32-mt(l)-1;l&=~(1<<u),n+=1;var i=32-mt(t)+u;if(30<i){var f=u-u%5;i=(l&(1<<f)-1).toString(32),l>>=f,u-=f,tn=1<<32-mt(t)+u|n<<u|l,nn=i+e}else tn=1<<i|n<<u|l,nn=e}function Wo(e){e.return!==null&&(ll(e,1),Ef(e,1,0))}function Fo(e){for(;e===Zu;)Zu=Ul[--jl],Ul[jl]=null,Ku=Ul[--jl],Ul[jl]=null;for(;e===nl;)nl=Rt[--Ot],Rt[Ot]=null,nn=Rt[--Ot],Rt[Ot]=null,tn=Rt[--Ot],Rt[Ot]=null}var lt=null,Be=null,Re=!1,al=null,kt=!1,$o=Error(r(519));function ul(e){var t=Error(r(418,""));throw Ca(Mt(t,e)),$o}function Af(e){var t=e.stateNode,n=e.type,l=e.memoizedProps;switch(t[Ie]=e,t[it]=l,n){case"dialog":Se("cancel",t),Se("close",t);break;case"iframe":case"object":case"embed":Se("load",t);break;case"video":case"audio":for(n=0;n<eu.length;n++)Se(eu[n],t);break;case"source":Se("error",t);break;case"img":case"image":case"link":Se("error",t),Se("load",t);break;case"details":Se("toggle",t);break;case"input":Se("invalid",t),Bs(t,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),zu(t);break;case"select":Se("invalid",t);break;case"textarea":Se("invalid",t),Gs(t,l.value,l.defaultValue,l.children),zu(t)}n=l.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||t.textContent===""+n||l.suppressHydrationWarning===!0||Ym(t.textContent,n)?(l.popover!=null&&(Se("beforetoggle",t),Se("toggle",t)),l.onScroll!=null&&Se("scroll",t),l.onScrollEnd!=null&&Se("scrollend",t),l.onClick!=null&&(t.onclick=Ri),t=!0):t=!1,t||ul(e)}function wf(e){for(lt=e.return;lt;)switch(lt.tag){case 5:case 13:kt=!1;return;case 27:case 3:kt=!0;return;default:lt=lt.return}}function Oa(e){if(e!==lt)return!1;if(!Re)return wf(e),Re=!0,!1;var t=e.tag,n;if((n=t!==3&&t!==27)&&((n=t===5)&&(n=e.type,n=!(n!=="form"&&n!=="button")||mc(e.type,e.memoizedProps)),n=!n),n&&Be&&ul(e),wf(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(r(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(n=e.data,n==="/$"){if(t===0){Be=jt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++;e=e.nextSibling}Be=null}}else t===27?(t=Be,Ln(e.type)?(e=pc,pc=null,Be=e):Be=t):Be=lt?jt(e.stateNode.nextSibling):null;return!0}function _a(){Be=lt=null,Re=!1}function Mf(){var e=al;return e!==null&&(st===null?st=e:st.push.apply(st,e),al=null),e}function Ca(e){al===null?al=[e]:al.push(e)}var Po=G(null),il=null,ln=null;function An(e,t,n){K(Po,t._currentValue),t._currentValue=n}function an(e){e._currentValue=Po.current,$(Po)}function Io(e,t,n){for(;e!==null;){var l=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,l!==null&&(l.childLanes|=t)):l!==null&&(l.childLanes&t)!==t&&(l.childLanes|=t),e===n)break;e=e.return}}function er(e,t,n,l){var u=e.child;for(u!==null&&(u.return=e);u!==null;){var i=u.dependencies;if(i!==null){var f=u.child;i=i.firstContext;e:for(;i!==null;){var m=i;i=u;for(var S=0;S<t.length;S++)if(m.context===t[S]){i.lanes|=n,m=i.alternate,m!==null&&(m.lanes|=n),Io(i.return,n,e),l||(f=null);break e}i=m.next}}else if(u.tag===18){if(f=u.return,f===null)throw Error(r(341));f.lanes|=n,i=f.alternate,i!==null&&(i.lanes|=n),Io(f,n,e),f=null}else f=u.child;if(f!==null)f.return=u;else for(f=u;f!==null;){if(f===e){f=null;break}if(u=f.sibling,u!==null){u.return=f.return,f=u;break}f=f.return}u=f}}function Da(e,t,n,l){e=null;for(var u=t,i=!1;u!==null;){if(!i){if((u.flags&524288)!==0)i=!0;else if((u.flags&262144)!==0)break}if(u.tag===10){var f=u.alternate;if(f===null)throw Error(r(387));if(f=f.memoizedProps,f!==null){var m=u.type;ht(u.pendingProps.value,f.value)||(e!==null?e.push(m):e=[m])}}else if(u===ue.current){if(f=u.alternate,f===null)throw Error(r(387));f.memoizedState.memoizedState!==u.memoizedState.memoizedState&&(e!==null?e.push(iu):e=[iu])}u=u.return}e!==null&&er(t,e,n,l),t.flags|=262144}function Ju(e){for(e=e.firstContext;e!==null;){if(!ht(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function ol(e){il=e,ln=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function et(e){return Tf(il,e)}function Wu(e,t){return il===null&&ol(e),Tf(e,t)}function Tf(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},ln===null){if(e===null)throw Error(r(308));ln=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else ln=ln.next=t;return n}var vy=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(n,l){e.push(l)}};this.abort=function(){t.aborted=!0,e.forEach(function(n){return n()})}},gy=a.unstable_scheduleCallback,py=a.unstable_NormalPriority,Xe={$$typeof:k,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function tr(){return{controller:new vy,data:new Map,refCount:0}}function Na(e){e.refCount--,e.refCount===0&&gy(py,function(){e.controller.abort()})}var za=null,nr=0,Hl=0,Bl=null;function yy(e,t){if(za===null){var n=za=[];nr=0,Hl=ac(),Bl={status:"pending",value:void 0,then:function(l){n.push(l)}}}return nr++,t.then(Rf,Rf),t}function Rf(){if(--nr===0&&za!==null){Bl!==null&&(Bl.status="fulfilled");var e=za;za=null,Hl=0,Bl=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function by(e,t){var n=[],l={status:"pending",value:null,reason:null,then:function(u){n.push(u)}};return e.then(function(){l.status="fulfilled",l.value=t;for(var u=0;u<n.length;u++)(0,n[u])(t)},function(u){for(l.status="rejected",l.reason=u,u=0;u<n.length;u++)(0,n[u])(void 0)}),l}var Of=O.S;O.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&yy(e,t),Of!==null&&Of(e,t)};var rl=G(null);function lr(){var e=rl.current;return e!==null?e:Ue.pooledCache}function Fu(e,t){t===null?K(rl,rl.current):K(rl,t.pool)}function _f(){var e=lr();return e===null?null:{parent:Xe._currentValue,pool:e}}var Ua=Error(r(460)),Cf=Error(r(474)),$u=Error(r(542)),ar={then:function(){}};function Df(e){return e=e.status,e==="fulfilled"||e==="rejected"}function Pu(){}function Nf(e,t,n){switch(n=e[n],n===void 0?e.push(t):n!==t&&(t.then(Pu,Pu),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Uf(e),e;default:if(typeof t.status=="string")t.then(Pu,Pu);else{if(e=Ue,e!==null&&100<e.shellSuspendCounter)throw Error(r(482));e=t,e.status="pending",e.then(function(l){if(t.status==="pending"){var u=t;u.status="fulfilled",u.value=l}},function(l){if(t.status==="pending"){var u=t;u.status="rejected",u.reason=l}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Uf(e),e}throw ja=t,Ua}}var ja=null;function zf(){if(ja===null)throw Error(r(459));var e=ja;return ja=null,e}function Uf(e){if(e===Ua||e===$u)throw Error(r(483))}var wn=!1;function ur(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function ir(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Mn(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Tn(e,t,n){var l=e.updateQueue;if(l===null)return null;if(l=l.shared,(Oe&2)!==0){var u=l.pending;return u===null?t.next=t:(t.next=u.next,u.next=t),l.pending=t,t=Vu(e),Sf(e,null,n),t}return Xu(e,l,t,n),Vu(e)}function Ha(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194048)!==0)){var l=t.lanes;l&=e.pendingLanes,n|=l,t.lanes=n,Ts(e,n)}}function or(e,t){var n=e.updateQueue,l=e.alternate;if(l!==null&&(l=l.updateQueue,n===l)){var u=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var f={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};i===null?u=i=f:i=i.next=f,n=n.next}while(n!==null);i===null?u=i=t:i=i.next=t}else u=i=t;n={baseState:l.baseState,firstBaseUpdate:u,lastBaseUpdate:i,shared:l.shared,callbacks:l.callbacks},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var rr=!1;function Ba(){if(rr){var e=Bl;if(e!==null)throw e}}function La(e,t,n,l){rr=!1;var u=e.updateQueue;wn=!1;var i=u.firstBaseUpdate,f=u.lastBaseUpdate,m=u.shared.pending;if(m!==null){u.shared.pending=null;var S=m,_=S.next;S.next=null,f===null?i=_:f.next=_,f=S;var B=e.alternate;B!==null&&(B=B.updateQueue,m=B.lastBaseUpdate,m!==f&&(m===null?B.firstBaseUpdate=_:m.next=_,B.lastBaseUpdate=S))}if(i!==null){var q=u.baseState;f=0,B=_=S=null,m=i;do{var D=m.lane&-536870913,N=D!==m.lane;if(N?(Ae&D)===D:(l&D)===D){D!==0&&D===Hl&&(rr=!0),B!==null&&(B=B.next={lane:0,tag:m.tag,payload:m.payload,callback:null,next:null});e:{var ce=e,ie=m;D=t;var Ne=n;switch(ie.tag){case 1:if(ce=ie.payload,typeof ce=="function"){q=ce.call(Ne,q,D);break e}q=ce;break e;case 3:ce.flags=ce.flags&-65537|128;case 0:if(ce=ie.payload,D=typeof ce=="function"?ce.call(Ne,q,D):ce,D==null)break e;q=p({},q,D);break e;case 2:wn=!0}}D=m.callback,D!==null&&(e.flags|=64,N&&(e.flags|=8192),N=u.callbacks,N===null?u.callbacks=[D]:N.push(D))}else N={lane:D,tag:m.tag,payload:m.payload,callback:m.callback,next:null},B===null?(_=B=N,S=q):B=B.next=N,f|=D;if(m=m.next,m===null){if(m=u.shared.pending,m===null)break;N=m,m=N.next,N.next=null,u.lastBaseUpdate=N,u.shared.pending=null}}while(!0);B===null&&(S=q),u.baseState=S,u.firstBaseUpdate=_,u.lastBaseUpdate=B,i===null&&(u.shared.lanes=0),Un|=f,e.lanes=f,e.memoizedState=q}}function jf(e,t){if(typeof e!="function")throw Error(r(191,e));e.call(t)}function Hf(e,t){var n=e.callbacks;if(n!==null)for(e.callbacks=null,e=0;e<n.length;e++)jf(n[e],t)}var Ll=G(null),Iu=G(0);function Bf(e,t){e=dn,K(Iu,e),K(Ll,t),dn=e|t.baseLanes}function cr(){K(Iu,dn),K(Ll,Ll.current)}function sr(){dn=Iu.current,$(Ll),$(Iu)}var Rn=0,ve=null,Ce=null,qe=null,ei=!1,Gl=!1,cl=!1,ti=0,Ga=0,Yl=null,Sy=0;function Ge(){throw Error(r(321))}function fr(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!ht(e[n],t[n]))return!1;return!0}function dr(e,t,n,l,u,i){return Rn=i,ve=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,O.H=e===null||e.memoizedState===null?Sd:xd,cl=!1,i=n(l,u),cl=!1,Gl&&(i=Gf(t,n,l,u)),Lf(e),i}function Lf(e){O.H=oi;var t=Ce!==null&&Ce.next!==null;if(Rn=0,qe=Ce=ve=null,ei=!1,Ga=0,Yl=null,t)throw Error(r(300));e===null||Ke||(e=e.dependencies,e!==null&&Ju(e)&&(Ke=!0))}function Gf(e,t,n,l){ve=e;var u=0;do{if(Gl&&(Yl=null),Ga=0,Gl=!1,25<=u)throw Error(r(301));if(u+=1,qe=Ce=null,e.updateQueue!=null){var i=e.updateQueue;i.lastEffect=null,i.events=null,i.stores=null,i.memoCache!=null&&(i.memoCache.index=0)}O.H=Ry,i=t(n,l)}while(Gl);return i}function xy(){var e=O.H,t=e.useState()[0];return t=typeof t.then=="function"?Ya(t):t,e=e.useState()[0],(Ce!==null?Ce.memoizedState:null)!==e&&(ve.flags|=1024),t}function mr(){var e=ti!==0;return ti=0,e}function hr(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function vr(e){if(ei){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}ei=!1}Rn=0,qe=Ce=ve=null,Gl=!1,Ga=ti=0,Yl=null}function rt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return qe===null?ve.memoizedState=qe=e:qe=qe.next=e,qe}function ke(){if(Ce===null){var e=ve.alternate;e=e!==null?e.memoizedState:null}else e=Ce.next;var t=qe===null?ve.memoizedState:qe.next;if(t!==null)qe=t,Ce=e;else{if(e===null)throw ve.alternate===null?Error(r(467)):Error(r(310));Ce=e,e={memoizedState:Ce.memoizedState,baseState:Ce.baseState,baseQueue:Ce.baseQueue,queue:Ce.queue,next:null},qe===null?ve.memoizedState=qe=e:qe=qe.next=e}return qe}function gr(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Ya(e){var t=Ga;return Ga+=1,Yl===null&&(Yl=[]),e=Nf(Yl,e,t),t=ve,(qe===null?t.memoizedState:qe.next)===null&&(t=t.alternate,O.H=t===null||t.memoizedState===null?Sd:xd),e}function ni(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return Ya(e);if(e.$$typeof===k)return et(e)}throw Error(r(438,String(e)))}function pr(e){var t=null,n=ve.updateQueue;if(n!==null&&(t=n.memoCache),t==null){var l=ve.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(t={data:l.data.map(function(u){return u.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),n===null&&(n=gr(),ve.updateQueue=n),n.memoCache=t,n=t.data[t.index],n===void 0)for(n=t.data[t.index]=Array(e),l=0;l<e;l++)n[l]=he;return t.index++,n}function un(e,t){return typeof t=="function"?t(e):t}function li(e){var t=ke();return yr(t,Ce,e)}function yr(e,t,n){var l=e.queue;if(l===null)throw Error(r(311));l.lastRenderedReducer=n;var u=e.baseQueue,i=l.pending;if(i!==null){if(u!==null){var f=u.next;u.next=i.next,i.next=f}t.baseQueue=u=i,l.pending=null}if(i=e.baseState,u===null)e.memoizedState=i;else{t=u.next;var m=f=null,S=null,_=t,B=!1;do{var q=_.lane&-536870913;if(q!==_.lane?(Ae&q)===q:(Rn&q)===q){var D=_.revertLane;if(D===0)S!==null&&(S=S.next={lane:0,revertLane:0,action:_.action,hasEagerState:_.hasEagerState,eagerState:_.eagerState,next:null}),q===Hl&&(B=!0);else if((Rn&D)===D){_=_.next,D===Hl&&(B=!0);continue}else q={lane:0,revertLane:_.revertLane,action:_.action,hasEagerState:_.hasEagerState,eagerState:_.eagerState,next:null},S===null?(m=S=q,f=i):S=S.next=q,ve.lanes|=D,Un|=D;q=_.action,cl&&n(i,q),i=_.hasEagerState?_.eagerState:n(i,q)}else D={lane:q,revertLane:_.revertLane,action:_.action,hasEagerState:_.hasEagerState,eagerState:_.eagerState,next:null},S===null?(m=S=D,f=i):S=S.next=D,ve.lanes|=q,Un|=q;_=_.next}while(_!==null&&_!==t);if(S===null?f=i:S.next=m,!ht(i,e.memoizedState)&&(Ke=!0,B&&(n=Bl,n!==null)))throw n;e.memoizedState=i,e.baseState=f,e.baseQueue=S,l.lastRenderedState=i}return u===null&&(l.lanes=0),[e.memoizedState,l.dispatch]}function br(e){var t=ke(),n=t.queue;if(n===null)throw Error(r(311));n.lastRenderedReducer=e;var l=n.dispatch,u=n.pending,i=t.memoizedState;if(u!==null){n.pending=null;var f=u=u.next;do i=e(i,f.action),f=f.next;while(f!==u);ht(i,t.memoizedState)||(Ke=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,l]}function Yf(e,t,n){var l=ve,u=ke(),i=Re;if(i){if(n===void 0)throw Error(r(407));n=n()}else n=t();var f=!ht((Ce||u).memoizedState,n);f&&(u.memoizedState=n,Ke=!0),u=u.queue;var m=Xf.bind(null,l,u,e);if(qa(2048,8,m,[e]),u.getSnapshot!==t||f||qe!==null&&qe.memoizedState.tag&1){if(l.flags|=2048,ql(9,ai(),kf.bind(null,l,u,n,t),null),Ue===null)throw Error(r(349));i||(Rn&124)!==0||qf(l,t,n)}return n}function qf(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ve.updateQueue,t===null?(t=gr(),ve.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function kf(e,t,n,l){t.value=n,t.getSnapshot=l,Vf(t)&&Qf(e)}function Xf(e,t,n){return n(function(){Vf(t)&&Qf(e)})}function Vf(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!ht(e,n)}catch{return!0}}function Qf(e){var t=Nl(e,2);t!==null&&St(t,e,2)}function Sr(e){var t=rt();if(typeof e=="function"){var n=e;if(e=n(),cl){Sn(!0);try{n()}finally{Sn(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:un,lastRenderedState:e},t}function Zf(e,t,n,l){return e.baseState=n,yr(e,Ce,typeof l=="function"?l:un)}function Ey(e,t,n,l,u){if(ii(e))throw Error(r(485));if(e=t.action,e!==null){var i={payload:u,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(f){i.listeners.push(f)}};O.T!==null?n(!0):i.isTransition=!1,l(i),n=t.pending,n===null?(i.next=t.pending=i,Kf(t,i)):(i.next=n.next,t.pending=n.next=i)}}function Kf(e,t){var n=t.action,l=t.payload,u=e.state;if(t.isTransition){var i=O.T,f={};O.T=f;try{var m=n(u,l),S=O.S;S!==null&&S(f,m),Jf(e,t,m)}catch(_){xr(e,t,_)}finally{O.T=i}}else try{i=n(u,l),Jf(e,t,i)}catch(_){xr(e,t,_)}}function Jf(e,t,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(l){Wf(e,t,l)},function(l){return xr(e,t,l)}):Wf(e,t,n)}function Wf(e,t,n){t.status="fulfilled",t.value=n,Ff(t),e.state=n,t=e.pending,t!==null&&(n=t.next,n===t?e.pending=null:(n=n.next,t.next=n,Kf(e,n)))}function xr(e,t,n){var l=e.pending;if(e.pending=null,l!==null){l=l.next;do t.status="rejected",t.reason=n,Ff(t),t=t.next;while(t!==l)}e.action=null}function Ff(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function $f(e,t){return t}function Pf(e,t){if(Re){var n=Ue.formState;if(n!==null){e:{var l=ve;if(Re){if(Be){t:{for(var u=Be,i=kt;u.nodeType!==8;){if(!i){u=null;break t}if(u=jt(u.nextSibling),u===null){u=null;break t}}i=u.data,u=i==="F!"||i==="F"?u:null}if(u){Be=jt(u.nextSibling),l=u.data==="F!";break e}}ul(l)}l=!1}l&&(t=n[0])}}return n=rt(),n.memoizedState=n.baseState=t,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:$f,lastRenderedState:t},n.queue=l,n=pd.bind(null,ve,l),l.dispatch=n,l=Sr(!1),i=Tr.bind(null,ve,!1,l.queue),l=rt(),u={state:t,dispatch:null,action:e,pending:null},l.queue=u,n=Ey.bind(null,ve,u,i,n),u.dispatch=n,l.memoizedState=e,[t,n,!1]}function If(e){var t=ke();return ed(t,Ce,e)}function ed(e,t,n){if(t=yr(e,t,$f)[0],e=li(un)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var l=Ya(t)}catch(f){throw f===Ua?$u:f}else l=t;t=ke();var u=t.queue,i=u.dispatch;return n!==t.memoizedState&&(ve.flags|=2048,ql(9,ai(),Ay.bind(null,u,n),null)),[l,i,e]}function Ay(e,t){e.action=t}function td(e){var t=ke(),n=Ce;if(n!==null)return ed(t,n,e);ke(),t=t.memoizedState,n=ke();var l=n.queue.dispatch;return n.memoizedState=e,[t,l,!1]}function ql(e,t,n,l){return e={tag:e,create:n,deps:l,inst:t,next:null},t=ve.updateQueue,t===null&&(t=gr(),ve.updateQueue=t),n=t.lastEffect,n===null?t.lastEffect=e.next=e:(l=n.next,n.next=e,e.next=l,t.lastEffect=e),e}function ai(){return{destroy:void 0,resource:void 0}}function nd(){return ke().memoizedState}function ui(e,t,n,l){var u=rt();l=l===void 0?null:l,ve.flags|=e,u.memoizedState=ql(1|t,ai(),n,l)}function qa(e,t,n,l){var u=ke();l=l===void 0?null:l;var i=u.memoizedState.inst;Ce!==null&&l!==null&&fr(l,Ce.memoizedState.deps)?u.memoizedState=ql(t,i,n,l):(ve.flags|=e,u.memoizedState=ql(1|t,i,n,l))}function ld(e,t){ui(8390656,8,e,t)}function ad(e,t){qa(2048,8,e,t)}function ud(e,t){return qa(4,2,e,t)}function id(e,t){return qa(4,4,e,t)}function od(e,t){if(typeof t=="function"){e=e();var n=t(e);return function(){typeof n=="function"?n():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function rd(e,t,n){n=n!=null?n.concat([e]):null,qa(4,4,od.bind(null,t,e),n)}function Er(){}function cd(e,t){var n=ke();t=t===void 0?null:t;var l=n.memoizedState;return t!==null&&fr(t,l[1])?l[0]:(n.memoizedState=[e,t],e)}function sd(e,t){var n=ke();t=t===void 0?null:t;var l=n.memoizedState;if(t!==null&&fr(t,l[1]))return l[0];if(l=e(),cl){Sn(!0);try{e()}finally{Sn(!1)}}return n.memoizedState=[l,t],l}function Ar(e,t,n){return n===void 0||(Rn&1073741824)!==0?e.memoizedState=t:(e.memoizedState=n,e=mm(),ve.lanes|=e,Un|=e,n)}function fd(e,t,n,l){return ht(n,t)?n:Ll.current!==null?(e=Ar(e,n,l),ht(e,t)||(Ke=!0),e):(Rn&42)===0?(Ke=!0,e.memoizedState=n):(e=mm(),ve.lanes|=e,Un|=e,t)}function dd(e,t,n,l,u){var i=Q.p;Q.p=i!==0&&8>i?i:8;var f=O.T,m={};O.T=m,Tr(e,!1,t,n);try{var S=u(),_=O.S;if(_!==null&&_(m,S),S!==null&&typeof S=="object"&&typeof S.then=="function"){var B=by(S,l);ka(e,t,B,bt(e))}else ka(e,t,l,bt(e))}catch(q){ka(e,t,{then:function(){},status:"rejected",reason:q},bt())}finally{Q.p=i,O.T=f}}function wy(){}function wr(e,t,n,l){if(e.tag!==5)throw Error(r(476));var u=md(e).queue;dd(e,u,t,j,n===null?wy:function(){return hd(e),n(l)})}function md(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:j,baseState:j,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:un,lastRenderedState:j},next:null};var n={};return t.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:un,lastRenderedState:n},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function hd(e){var t=md(e).next.queue;ka(e,t,{},bt())}function Mr(){return et(iu)}function vd(){return ke().memoizedState}function gd(){return ke().memoizedState}function My(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var n=bt();e=Mn(n);var l=Tn(t,e,n);l!==null&&(St(l,t,n),Ha(l,t,n)),t={cache:tr()},e.payload=t;return}t=t.return}}function Ty(e,t,n){var l=bt();n={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},ii(e)?yd(t,n):(n=Qo(e,t,n,l),n!==null&&(St(n,e,l),bd(n,t,l)))}function pd(e,t,n){var l=bt();ka(e,t,n,l)}function ka(e,t,n,l){var u={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(ii(e))yd(t,u);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var f=t.lastRenderedState,m=i(f,n);if(u.hasEagerState=!0,u.eagerState=m,ht(m,f))return Xu(e,t,u,0),Ue===null&&ku(),!1}catch{}finally{}if(n=Qo(e,t,u,l),n!==null)return St(n,e,l),bd(n,t,l),!0}return!1}function Tr(e,t,n,l){if(l={lane:2,revertLane:ac(),action:l,hasEagerState:!1,eagerState:null,next:null},ii(e)){if(t)throw Error(r(479))}else t=Qo(e,n,l,2),t!==null&&St(t,e,2)}function ii(e){var t=e.alternate;return e===ve||t!==null&&t===ve}function yd(e,t){Gl=ei=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function bd(e,t,n){if((n&4194048)!==0){var l=t.lanes;l&=e.pendingLanes,n|=l,t.lanes=n,Ts(e,n)}}var oi={readContext:et,use:ni,useCallback:Ge,useContext:Ge,useEffect:Ge,useImperativeHandle:Ge,useLayoutEffect:Ge,useInsertionEffect:Ge,useMemo:Ge,useReducer:Ge,useRef:Ge,useState:Ge,useDebugValue:Ge,useDeferredValue:Ge,useTransition:Ge,useSyncExternalStore:Ge,useId:Ge,useHostTransitionStatus:Ge,useFormState:Ge,useActionState:Ge,useOptimistic:Ge,useMemoCache:Ge,useCacheRefresh:Ge},Sd={readContext:et,use:ni,useCallback:function(e,t){return rt().memoizedState=[e,t===void 0?null:t],e},useContext:et,useEffect:ld,useImperativeHandle:function(e,t,n){n=n!=null?n.concat([e]):null,ui(4194308,4,od.bind(null,t,e),n)},useLayoutEffect:function(e,t){return ui(4194308,4,e,t)},useInsertionEffect:function(e,t){ui(4,2,e,t)},useMemo:function(e,t){var n=rt();t=t===void 0?null:t;var l=e();if(cl){Sn(!0);try{e()}finally{Sn(!1)}}return n.memoizedState=[l,t],l},useReducer:function(e,t,n){var l=rt();if(n!==void 0){var u=n(t);if(cl){Sn(!0);try{n(t)}finally{Sn(!1)}}}else u=t;return l.memoizedState=l.baseState=u,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:u},l.queue=e,e=e.dispatch=Ty.bind(null,ve,e),[l.memoizedState,e]},useRef:function(e){var t=rt();return e={current:e},t.memoizedState=e},useState:function(e){e=Sr(e);var t=e.queue,n=pd.bind(null,ve,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:Er,useDeferredValue:function(e,t){var n=rt();return Ar(n,e,t)},useTransition:function(){var e=Sr(!1);return e=dd.bind(null,ve,e.queue,!0,!1),rt().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var l=ve,u=rt();if(Re){if(n===void 0)throw Error(r(407));n=n()}else{if(n=t(),Ue===null)throw Error(r(349));(Ae&124)!==0||qf(l,t,n)}u.memoizedState=n;var i={value:n,getSnapshot:t};return u.queue=i,ld(Xf.bind(null,l,i,e),[e]),l.flags|=2048,ql(9,ai(),kf.bind(null,l,i,n,t),null),n},useId:function(){var e=rt(),t=Ue.identifierPrefix;if(Re){var n=nn,l=tn;n=(l&~(1<<32-mt(l)-1)).toString(32)+n,t="«"+t+"R"+n,n=ti++,0<n&&(t+="H"+n.toString(32)),t+="»"}else n=Sy++,t="«"+t+"r"+n.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:Mr,useFormState:Pf,useActionState:Pf,useOptimistic:function(e){var t=rt();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=Tr.bind(null,ve,!0,n),n.dispatch=t,[e,t]},useMemoCache:pr,useCacheRefresh:function(){return rt().memoizedState=My.bind(null,ve)}},xd={readContext:et,use:ni,useCallback:cd,useContext:et,useEffect:ad,useImperativeHandle:rd,useInsertionEffect:ud,useLayoutEffect:id,useMemo:sd,useReducer:li,useRef:nd,useState:function(){return li(un)},useDebugValue:Er,useDeferredValue:function(e,t){var n=ke();return fd(n,Ce.memoizedState,e,t)},useTransition:function(){var e=li(un)[0],t=ke().memoizedState;return[typeof e=="boolean"?e:Ya(e),t]},useSyncExternalStore:Yf,useId:vd,useHostTransitionStatus:Mr,useFormState:If,useActionState:If,useOptimistic:function(e,t){var n=ke();return Zf(n,Ce,e,t)},useMemoCache:pr,useCacheRefresh:gd},Ry={readContext:et,use:ni,useCallback:cd,useContext:et,useEffect:ad,useImperativeHandle:rd,useInsertionEffect:ud,useLayoutEffect:id,useMemo:sd,useReducer:br,useRef:nd,useState:function(){return br(un)},useDebugValue:Er,useDeferredValue:function(e,t){var n=ke();return Ce===null?Ar(n,e,t):fd(n,Ce.memoizedState,e,t)},useTransition:function(){var e=br(un)[0],t=ke().memoizedState;return[typeof e=="boolean"?e:Ya(e),t]},useSyncExternalStore:Yf,useId:vd,useHostTransitionStatus:Mr,useFormState:td,useActionState:td,useOptimistic:function(e,t){var n=ke();return Ce!==null?Zf(n,Ce,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:pr,useCacheRefresh:gd},kl=null,Xa=0;function ri(e){var t=Xa;return Xa+=1,kl===null&&(kl=[]),Nf(kl,e,t)}function Va(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function ci(e,t){throw t.$$typeof===x?Error(r(525)):(e=Object.prototype.toString.call(t),Error(r(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function Ed(e){var t=e._init;return t(e._payload)}function Ad(e){function t(M,A){if(e){var R=M.deletions;R===null?(M.deletions=[A],M.flags|=16):R.push(A)}}function n(M,A){if(!e)return null;for(;A!==null;)t(M,A),A=A.sibling;return null}function l(M){for(var A=new Map;M!==null;)M.key!==null?A.set(M.key,M):A.set(M.index,M),M=M.sibling;return A}function u(M,A){return M=en(M,A),M.index=0,M.sibling=null,M}function i(M,A,R){return M.index=R,e?(R=M.alternate,R!==null?(R=R.index,R<A?(M.flags|=67108866,A):R):(M.flags|=67108866,A)):(M.flags|=1048576,A)}function f(M){return e&&M.alternate===null&&(M.flags|=67108866),M}function m(M,A,R,L){return A===null||A.tag!==6?(A=Ko(R,M.mode,L),A.return=M,A):(A=u(A,R),A.return=M,A)}function S(M,A,R,L){var ee=R.type;return ee===z?B(M,A,R.props.children,L,R.key):A!==null&&(A.elementType===ee||typeof ee=="object"&&ee!==null&&ee.$$typeof===Z&&Ed(ee)===A.type)?(A=u(A,R.props),Va(A,R),A.return=M,A):(A=Qu(R.type,R.key,R.props,null,M.mode,L),Va(A,R),A.return=M,A)}function _(M,A,R,L){return A===null||A.tag!==4||A.stateNode.containerInfo!==R.containerInfo||A.stateNode.implementation!==R.implementation?(A=Jo(R,M.mode,L),A.return=M,A):(A=u(A,R.children||[]),A.return=M,A)}function B(M,A,R,L,ee){return A===null||A.tag!==7?(A=tl(R,M.mode,L,ee),A.return=M,A):(A=u(A,R),A.return=M,A)}function q(M,A,R){if(typeof A=="string"&&A!==""||typeof A=="number"||typeof A=="bigint")return A=Ko(""+A,M.mode,R),A.return=M,A;if(typeof A=="object"&&A!==null){switch(A.$$typeof){case w:return R=Qu(A.type,A.key,A.props,null,M.mode,R),Va(R,A),R.return=M,R;case C:return A=Jo(A,M.mode,R),A.return=M,A;case Z:var L=A._init;return A=L(A._payload),q(M,A,R)}if(de(A)||se(A))return A=tl(A,M.mode,R,null),A.return=M,A;if(typeof A.then=="function")return q(M,ri(A),R);if(A.$$typeof===k)return q(M,Wu(M,A),R);ci(M,A)}return null}function D(M,A,R,L){var ee=A!==null?A.key:null;if(typeof R=="string"&&R!==""||typeof R=="number"||typeof R=="bigint")return ee!==null?null:m(M,A,""+R,L);if(typeof R=="object"&&R!==null){switch(R.$$typeof){case w:return R.key===ee?S(M,A,R,L):null;case C:return R.key===ee?_(M,A,R,L):null;case Z:return ee=R._init,R=ee(R._payload),D(M,A,R,L)}if(de(R)||se(R))return ee!==null?null:B(M,A,R,L,null);if(typeof R.then=="function")return D(M,A,ri(R),L);if(R.$$typeof===k)return D(M,A,Wu(M,R),L);ci(M,R)}return null}function N(M,A,R,L,ee){if(typeof L=="string"&&L!==""||typeof L=="number"||typeof L=="bigint")return M=M.get(R)||null,m(A,M,""+L,ee);if(typeof L=="object"&&L!==null){switch(L.$$typeof){case w:return M=M.get(L.key===null?R:L.key)||null,S(A,M,L,ee);case C:return M=M.get(L.key===null?R:L.key)||null,_(A,M,L,ee);case Z:var ye=L._init;return L=ye(L._payload),N(M,A,R,L,ee)}if(de(L)||se(L))return M=M.get(R)||null,B(A,M,L,ee,null);if(typeof L.then=="function")return N(M,A,R,ri(L),ee);if(L.$$typeof===k)return N(M,A,R,Wu(A,L),ee);ci(A,L)}return null}function ce(M,A,R,L){for(var ee=null,ye=null,le=A,oe=A=0,We=null;le!==null&&oe<R.length;oe++){le.index>oe?(We=le,le=null):We=le.sibling;var Te=D(M,le,R[oe],L);if(Te===null){le===null&&(le=We);break}e&&le&&Te.alternate===null&&t(M,le),A=i(Te,A,oe),ye===null?ee=Te:ye.sibling=Te,ye=Te,le=We}if(oe===R.length)return n(M,le),Re&&ll(M,oe),ee;if(le===null){for(;oe<R.length;oe++)le=q(M,R[oe],L),le!==null&&(A=i(le,A,oe),ye===null?ee=le:ye.sibling=le,ye=le);return Re&&ll(M,oe),ee}for(le=l(le);oe<R.length;oe++)We=N(le,M,oe,R[oe],L),We!==null&&(e&&We.alternate!==null&&le.delete(We.key===null?oe:We.key),A=i(We,A,oe),ye===null?ee=We:ye.sibling=We,ye=We);return e&&le.forEach(function(Xn){return t(M,Xn)}),Re&&ll(M,oe),ee}function ie(M,A,R,L){if(R==null)throw Error(r(151));for(var ee=null,ye=null,le=A,oe=A=0,We=null,Te=R.next();le!==null&&!Te.done;oe++,Te=R.next()){le.index>oe?(We=le,le=null):We=le.sibling;var Xn=D(M,le,Te.value,L);if(Xn===null){le===null&&(le=We);break}e&&le&&Xn.alternate===null&&t(M,le),A=i(Xn,A,oe),ye===null?ee=Xn:ye.sibling=Xn,ye=Xn,le=We}if(Te.done)return n(M,le),Re&&ll(M,oe),ee;if(le===null){for(;!Te.done;oe++,Te=R.next())Te=q(M,Te.value,L),Te!==null&&(A=i(Te,A,oe),ye===null?ee=Te:ye.sibling=Te,ye=Te);return Re&&ll(M,oe),ee}for(le=l(le);!Te.done;oe++,Te=R.next())Te=N(le,M,oe,Te.value,L),Te!==null&&(e&&Te.alternate!==null&&le.delete(Te.key===null?oe:Te.key),A=i(Te,A,oe),ye===null?ee=Te:ye.sibling=Te,ye=Te);return e&&le.forEach(function(O0){return t(M,O0)}),Re&&ll(M,oe),ee}function Ne(M,A,R,L){if(typeof R=="object"&&R!==null&&R.type===z&&R.key===null&&(R=R.props.children),typeof R=="object"&&R!==null){switch(R.$$typeof){case w:e:{for(var ee=R.key;A!==null;){if(A.key===ee){if(ee=R.type,ee===z){if(A.tag===7){n(M,A.sibling),L=u(A,R.props.children),L.return=M,M=L;break e}}else if(A.elementType===ee||typeof ee=="object"&&ee!==null&&ee.$$typeof===Z&&Ed(ee)===A.type){n(M,A.sibling),L=u(A,R.props),Va(L,R),L.return=M,M=L;break e}n(M,A);break}else t(M,A);A=A.sibling}R.type===z?(L=tl(R.props.children,M.mode,L,R.key),L.return=M,M=L):(L=Qu(R.type,R.key,R.props,null,M.mode,L),Va(L,R),L.return=M,M=L)}return f(M);case C:e:{for(ee=R.key;A!==null;){if(A.key===ee)if(A.tag===4&&A.stateNode.containerInfo===R.containerInfo&&A.stateNode.implementation===R.implementation){n(M,A.sibling),L=u(A,R.children||[]),L.return=M,M=L;break e}else{n(M,A);break}else t(M,A);A=A.sibling}L=Jo(R,M.mode,L),L.return=M,M=L}return f(M);case Z:return ee=R._init,R=ee(R._payload),Ne(M,A,R,L)}if(de(R))return ce(M,A,R,L);if(se(R)){if(ee=se(R),typeof ee!="function")throw Error(r(150));return R=ee.call(R),ie(M,A,R,L)}if(typeof R.then=="function")return Ne(M,A,ri(R),L);if(R.$$typeof===k)return Ne(M,A,Wu(M,R),L);ci(M,R)}return typeof R=="string"&&R!==""||typeof R=="number"||typeof R=="bigint"?(R=""+R,A!==null&&A.tag===6?(n(M,A.sibling),L=u(A,R),L.return=M,M=L):(n(M,A),L=Ko(R,M.mode,L),L.return=M,M=L),f(M)):n(M,A)}return function(M,A,R,L){try{Xa=0;var ee=Ne(M,A,R,L);return kl=null,ee}catch(le){if(le===Ua||le===$u)throw le;var ye=vt(29,le,null,M.mode);return ye.lanes=L,ye.return=M,ye}finally{}}}var Xl=Ad(!0),wd=Ad(!1),_t=G(null),Xt=null;function On(e){var t=e.alternate;K(Ve,Ve.current&1),K(_t,e),Xt===null&&(t===null||Ll.current!==null||t.memoizedState!==null)&&(Xt=e)}function Md(e){if(e.tag===22){if(K(Ve,Ve.current),K(_t,e),Xt===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(Xt=e)}}else _n()}function _n(){K(Ve,Ve.current),K(_t,_t.current)}function on(e){$(_t),Xt===e&&(Xt=null),$(Ve)}var Ve=G(0);function si(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||gc(n)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function Rr(e,t,n,l){t=e.memoizedState,n=n(l,t),n=n==null?t:p({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Or={enqueueSetState:function(e,t,n){e=e._reactInternals;var l=bt(),u=Mn(l);u.payload=t,n!=null&&(u.callback=n),t=Tn(e,u,l),t!==null&&(St(t,e,l),Ha(t,e,l))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var l=bt(),u=Mn(l);u.tag=1,u.payload=t,n!=null&&(u.callback=n),t=Tn(e,u,l),t!==null&&(St(t,e,l),Ha(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=bt(),l=Mn(n);l.tag=2,t!=null&&(l.callback=t),t=Tn(e,l,n),t!==null&&(St(t,e,n),Ha(t,e,n))}};function Td(e,t,n,l,u,i,f){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(l,i,f):t.prototype&&t.prototype.isPureReactComponent?!Ta(n,l)||!Ta(u,i):!0}function Rd(e,t,n,l){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,l),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,l),t.state!==e&&Or.enqueueReplaceState(t,t.state,null)}function sl(e,t){var n=t;if("ref"in t){n={};for(var l in t)l!=="ref"&&(n[l]=t[l])}if(e=e.defaultProps){n===t&&(n=p({},n));for(var u in e)n[u]===void 0&&(n[u]=e[u])}return n}var fi=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Od(e){fi(e)}function _d(e){console.error(e)}function Cd(e){fi(e)}function di(e,t){try{var n=e.onUncaughtError;n(t.value,{componentStack:t.stack})}catch(l){setTimeout(function(){throw l})}}function Dd(e,t,n){try{var l=e.onCaughtError;l(n.value,{componentStack:n.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(u){setTimeout(function(){throw u})}}function _r(e,t,n){return n=Mn(n),n.tag=3,n.payload={element:null},n.callback=function(){di(e,t)},n}function Nd(e){return e=Mn(e),e.tag=3,e}function zd(e,t,n,l){var u=n.type.getDerivedStateFromError;if(typeof u=="function"){var i=l.value;e.payload=function(){return u(i)},e.callback=function(){Dd(t,n,l)}}var f=n.stateNode;f!==null&&typeof f.componentDidCatch=="function"&&(e.callback=function(){Dd(t,n,l),typeof u!="function"&&(jn===null?jn=new Set([this]):jn.add(this));var m=l.stack;this.componentDidCatch(l.value,{componentStack:m!==null?m:""})})}function Oy(e,t,n,l,u){if(n.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(t=n.alternate,t!==null&&Da(t,n,u,!0),n=_t.current,n!==null){switch(n.tag){case 13:return Xt===null?Ir():n.alternate===null&&Le===0&&(Le=3),n.flags&=-257,n.flags|=65536,n.lanes=u,l===ar?n.flags|=16384:(t=n.updateQueue,t===null?n.updateQueue=new Set([l]):t.add(l),tc(e,l,u)),!1;case 22:return n.flags|=65536,l===ar?n.flags|=16384:(t=n.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([l])},n.updateQueue=t):(n=t.retryQueue,n===null?t.retryQueue=new Set([l]):n.add(l)),tc(e,l,u)),!1}throw Error(r(435,n.tag))}return tc(e,l,u),Ir(),!1}if(Re)return t=_t.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=u,l!==$o&&(e=Error(r(422),{cause:l}),Ca(Mt(e,n)))):(l!==$o&&(t=Error(r(423),{cause:l}),Ca(Mt(t,n))),e=e.current.alternate,e.flags|=65536,u&=-u,e.lanes|=u,l=Mt(l,n),u=_r(e.stateNode,l,u),or(e,u),Le!==4&&(Le=2)),!1;var i=Error(r(520),{cause:l});if(i=Mt(i,n),$a===null?$a=[i]:$a.push(i),Le!==4&&(Le=2),t===null)return!0;l=Mt(l,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=u&-u,n.lanes|=e,e=_r(n.stateNode,l,e),or(n,e),!1;case 1:if(t=n.type,i=n.stateNode,(n.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||i!==null&&typeof i.componentDidCatch=="function"&&(jn===null||!jn.has(i))))return n.flags|=65536,u&=-u,n.lanes|=u,u=Nd(u),zd(u,e,n,l),or(n,u),!1}n=n.return}while(n!==null);return!1}var Ud=Error(r(461)),Ke=!1;function Fe(e,t,n,l){t.child=e===null?wd(t,null,n,l):Xl(t,e.child,n,l)}function jd(e,t,n,l,u){n=n.render;var i=t.ref;if("ref"in l){var f={};for(var m in l)m!=="ref"&&(f[m]=l[m])}else f=l;return ol(t),l=dr(e,t,n,f,i,u),m=mr(),e!==null&&!Ke?(hr(e,t,u),rn(e,t,u)):(Re&&m&&Wo(t),t.flags|=1,Fe(e,t,l,u),t.child)}function Hd(e,t,n,l,u){if(e===null){var i=n.type;return typeof i=="function"&&!Zo(i)&&i.defaultProps===void 0&&n.compare===null?(t.tag=15,t.type=i,Bd(e,t,i,l,u)):(e=Qu(n.type,null,l,t,t.mode,u),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!Br(e,u)){var f=i.memoizedProps;if(n=n.compare,n=n!==null?n:Ta,n(f,l)&&e.ref===t.ref)return rn(e,t,u)}return t.flags|=1,e=en(i,l),e.ref=t.ref,e.return=t,t.child=e}function Bd(e,t,n,l,u){if(e!==null){var i=e.memoizedProps;if(Ta(i,l)&&e.ref===t.ref)if(Ke=!1,t.pendingProps=l=i,Br(e,u))(e.flags&131072)!==0&&(Ke=!0);else return t.lanes=e.lanes,rn(e,t,u)}return Cr(e,t,n,l,u)}function Ld(e,t,n){var l=t.pendingProps,u=l.children,i=e!==null?e.memoizedState:null;if(l.mode==="hidden"){if((t.flags&128)!==0){if(l=i!==null?i.baseLanes|n:n,e!==null){for(u=t.child=e.child,i=0;u!==null;)i=i|u.lanes|u.childLanes,u=u.sibling;t.childLanes=i&~l}else t.childLanes=0,t.child=null;return Gd(e,t,l,n)}if((n&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&Fu(t,i!==null?i.cachePool:null),i!==null?Bf(t,i):cr(),Md(t);else return t.lanes=t.childLanes=536870912,Gd(e,t,i!==null?i.baseLanes|n:n,n)}else i!==null?(Fu(t,i.cachePool),Bf(t,i),_n(),t.memoizedState=null):(e!==null&&Fu(t,null),cr(),_n());return Fe(e,t,u,n),t.child}function Gd(e,t,n,l){var u=lr();return u=u===null?null:{parent:Xe._currentValue,pool:u},t.memoizedState={baseLanes:n,cachePool:u},e!==null&&Fu(t,null),cr(),Md(t),e!==null&&Da(e,t,l,!0),null}function mi(e,t){var n=t.ref;if(n===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(r(284));(e===null||e.ref!==n)&&(t.flags|=4194816)}}function Cr(e,t,n,l,u){return ol(t),n=dr(e,t,n,l,void 0,u),l=mr(),e!==null&&!Ke?(hr(e,t,u),rn(e,t,u)):(Re&&l&&Wo(t),t.flags|=1,Fe(e,t,n,u),t.child)}function Yd(e,t,n,l,u,i){return ol(t),t.updateQueue=null,n=Gf(t,l,n,u),Lf(e),l=mr(),e!==null&&!Ke?(hr(e,t,i),rn(e,t,i)):(Re&&l&&Wo(t),t.flags|=1,Fe(e,t,n,i),t.child)}function qd(e,t,n,l,u){if(ol(t),t.stateNode===null){var i=zl,f=n.contextType;typeof f=="object"&&f!==null&&(i=et(f)),i=new n(l,i),t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,i.updater=Or,t.stateNode=i,i._reactInternals=t,i=t.stateNode,i.props=l,i.state=t.memoizedState,i.refs={},ur(t),f=n.contextType,i.context=typeof f=="object"&&f!==null?et(f):zl,i.state=t.memoizedState,f=n.getDerivedStateFromProps,typeof f=="function"&&(Rr(t,n,f,l),i.state=t.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(f=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),f!==i.state&&Or.enqueueReplaceState(i,i.state,null),La(t,l,i,u),Ba(),i.state=t.memoizedState),typeof i.componentDidMount=="function"&&(t.flags|=4194308),l=!0}else if(e===null){i=t.stateNode;var m=t.memoizedProps,S=sl(n,m);i.props=S;var _=i.context,B=n.contextType;f=zl,typeof B=="object"&&B!==null&&(f=et(B));var q=n.getDerivedStateFromProps;B=typeof q=="function"||typeof i.getSnapshotBeforeUpdate=="function",m=t.pendingProps!==m,B||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(m||_!==f)&&Rd(t,i,l,f),wn=!1;var D=t.memoizedState;i.state=D,La(t,l,i,u),Ba(),_=t.memoizedState,m||D!==_||wn?(typeof q=="function"&&(Rr(t,n,q,l),_=t.memoizedState),(S=wn||Td(t,n,S,l,D,_,f))?(B||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=l,t.memoizedState=_),i.props=l,i.state=_,i.context=f,l=S):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),l=!1)}else{i=t.stateNode,ir(e,t),f=t.memoizedProps,B=sl(n,f),i.props=B,q=t.pendingProps,D=i.context,_=n.contextType,S=zl,typeof _=="object"&&_!==null&&(S=et(_)),m=n.getDerivedStateFromProps,(_=typeof m=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(f!==q||D!==S)&&Rd(t,i,l,S),wn=!1,D=t.memoizedState,i.state=D,La(t,l,i,u),Ba();var N=t.memoizedState;f!==q||D!==N||wn||e!==null&&e.dependencies!==null&&Ju(e.dependencies)?(typeof m=="function"&&(Rr(t,n,m,l),N=t.memoizedState),(B=wn||Td(t,n,B,l,D,N,S)||e!==null&&e.dependencies!==null&&Ju(e.dependencies))?(_||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(l,N,S),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(l,N,S)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||f===e.memoizedProps&&D===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||f===e.memoizedProps&&D===e.memoizedState||(t.flags|=1024),t.memoizedProps=l,t.memoizedState=N),i.props=l,i.state=N,i.context=S,l=B):(typeof i.componentDidUpdate!="function"||f===e.memoizedProps&&D===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||f===e.memoizedProps&&D===e.memoizedState||(t.flags|=1024),l=!1)}return i=l,mi(e,t),l=(t.flags&128)!==0,i||l?(i=t.stateNode,n=l&&typeof n.getDerivedStateFromError!="function"?null:i.render(),t.flags|=1,e!==null&&l?(t.child=Xl(t,e.child,null,u),t.child=Xl(t,null,n,u)):Fe(e,t,n,u),t.memoizedState=i.state,e=t.child):e=rn(e,t,u),e}function kd(e,t,n,l){return _a(),t.flags|=256,Fe(e,t,n,l),t.child}var Dr={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Nr(e){return{baseLanes:e,cachePool:_f()}}function zr(e,t,n){return e=e!==null?e.childLanes&~n:0,t&&(e|=Ct),e}function Xd(e,t,n){var l=t.pendingProps,u=!1,i=(t.flags&128)!==0,f;if((f=i)||(f=e!==null&&e.memoizedState===null?!1:(Ve.current&2)!==0),f&&(u=!0,t.flags&=-129),f=(t.flags&32)!==0,t.flags&=-33,e===null){if(Re){if(u?On(t):_n(),Re){var m=Be,S;if(S=m){e:{for(S=m,m=kt;S.nodeType!==8;){if(!m){m=null;break e}if(S=jt(S.nextSibling),S===null){m=null;break e}}m=S}m!==null?(t.memoizedState={dehydrated:m,treeContext:nl!==null?{id:tn,overflow:nn}:null,retryLane:536870912,hydrationErrors:null},S=vt(18,null,null,0),S.stateNode=m,S.return=t,t.child=S,lt=t,Be=null,S=!0):S=!1}S||ul(t)}if(m=t.memoizedState,m!==null&&(m=m.dehydrated,m!==null))return gc(m)?t.lanes=32:t.lanes=536870912,null;on(t)}return m=l.children,l=l.fallback,u?(_n(),u=t.mode,m=hi({mode:"hidden",children:m},u),l=tl(l,u,n,null),m.return=t,l.return=t,m.sibling=l,t.child=m,u=t.child,u.memoizedState=Nr(n),u.childLanes=zr(e,f,n),t.memoizedState=Dr,l):(On(t),Ur(t,m))}if(S=e.memoizedState,S!==null&&(m=S.dehydrated,m!==null)){if(i)t.flags&256?(On(t),t.flags&=-257,t=jr(e,t,n)):t.memoizedState!==null?(_n(),t.child=e.child,t.flags|=128,t=null):(_n(),u=l.fallback,m=t.mode,l=hi({mode:"visible",children:l.children},m),u=tl(u,m,n,null),u.flags|=2,l.return=t,u.return=t,l.sibling=u,t.child=l,Xl(t,e.child,null,n),l=t.child,l.memoizedState=Nr(n),l.childLanes=zr(e,f,n),t.memoizedState=Dr,t=u);else if(On(t),gc(m)){if(f=m.nextSibling&&m.nextSibling.dataset,f)var _=f.dgst;f=_,l=Error(r(419)),l.stack="",l.digest=f,Ca({value:l,source:null,stack:null}),t=jr(e,t,n)}else if(Ke||Da(e,t,n,!1),f=(n&e.childLanes)!==0,Ke||f){if(f=Ue,f!==null&&(l=n&-n,l=(l&42)!==0?1:po(l),l=(l&(f.suspendedLanes|n))!==0?0:l,l!==0&&l!==S.retryLane))throw S.retryLane=l,Nl(e,l),St(f,e,l),Ud;m.data==="$?"||Ir(),t=jr(e,t,n)}else m.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=S.treeContext,Be=jt(m.nextSibling),lt=t,Re=!0,al=null,kt=!1,e!==null&&(Rt[Ot++]=tn,Rt[Ot++]=nn,Rt[Ot++]=nl,tn=e.id,nn=e.overflow,nl=t),t=Ur(t,l.children),t.flags|=4096);return t}return u?(_n(),u=l.fallback,m=t.mode,S=e.child,_=S.sibling,l=en(S,{mode:"hidden",children:l.children}),l.subtreeFlags=S.subtreeFlags&65011712,_!==null?u=en(_,u):(u=tl(u,m,n,null),u.flags|=2),u.return=t,l.return=t,l.sibling=u,t.child=l,l=u,u=t.child,m=e.child.memoizedState,m===null?m=Nr(n):(S=m.cachePool,S!==null?(_=Xe._currentValue,S=S.parent!==_?{parent:_,pool:_}:S):S=_f(),m={baseLanes:m.baseLanes|n,cachePool:S}),u.memoizedState=m,u.childLanes=zr(e,f,n),t.memoizedState=Dr,l):(On(t),n=e.child,e=n.sibling,n=en(n,{mode:"visible",children:l.children}),n.return=t,n.sibling=null,e!==null&&(f=t.deletions,f===null?(t.deletions=[e],t.flags|=16):f.push(e)),t.child=n,t.memoizedState=null,n)}function Ur(e,t){return t=hi({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function hi(e,t){return e=vt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function jr(e,t,n){return Xl(t,e.child,null,n),e=Ur(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Vd(e,t,n){e.lanes|=t;var l=e.alternate;l!==null&&(l.lanes|=t),Io(e.return,t,n)}function Hr(e,t,n,l,u){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:l,tail:n,tailMode:u}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=l,i.tail=n,i.tailMode=u)}function Qd(e,t,n){var l=t.pendingProps,u=l.revealOrder,i=l.tail;if(Fe(e,t,l.children,n),l=Ve.current,(l&2)!==0)l=l&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Vd(e,n,t);else if(e.tag===19)Vd(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}l&=1}switch(K(Ve,l),u){case"forwards":for(n=t.child,u=null;n!==null;)e=n.alternate,e!==null&&si(e)===null&&(u=n),n=n.sibling;n=u,n===null?(u=t.child,t.child=null):(u=n.sibling,n.sibling=null),Hr(t,!1,u,n,i);break;case"backwards":for(n=null,u=t.child,t.child=null;u!==null;){if(e=u.alternate,e!==null&&si(e)===null){t.child=u;break}e=u.sibling,u.sibling=n,n=u,u=e}Hr(t,!0,n,null,i);break;case"together":Hr(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function rn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Un|=t.lanes,(n&t.childLanes)===0)if(e!==null){if(Da(e,t,n,!1),(n&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(r(153));if(t.child!==null){for(e=t.child,n=en(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=en(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Br(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&Ju(e)))}function _y(e,t,n){switch(t.tag){case 3:Me(t,t.stateNode.containerInfo),An(t,Xe,e.memoizedState.cache),_a();break;case 27:case 5:Lt(t);break;case 4:Me(t,t.stateNode.containerInfo);break;case 10:An(t,t.type,t.memoizedProps.value);break;case 13:var l=t.memoizedState;if(l!==null)return l.dehydrated!==null?(On(t),t.flags|=128,null):(n&t.child.childLanes)!==0?Xd(e,t,n):(On(t),e=rn(e,t,n),e!==null?e.sibling:null);On(t);break;case 19:var u=(e.flags&128)!==0;if(l=(n&t.childLanes)!==0,l||(Da(e,t,n,!1),l=(n&t.childLanes)!==0),u){if(l)return Qd(e,t,n);t.flags|=128}if(u=t.memoizedState,u!==null&&(u.rendering=null,u.tail=null,u.lastEffect=null),K(Ve,Ve.current),l)break;return null;case 22:case 23:return t.lanes=0,Ld(e,t,n);case 24:An(t,Xe,e.memoizedState.cache)}return rn(e,t,n)}function Zd(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps)Ke=!0;else{if(!Br(e,n)&&(t.flags&128)===0)return Ke=!1,_y(e,t,n);Ke=(e.flags&131072)!==0}else Ke=!1,Re&&(t.flags&1048576)!==0&&Ef(t,Ku,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var l=t.elementType,u=l._init;if(l=u(l._payload),t.type=l,typeof l=="function")Zo(l)?(e=sl(l,e),t.tag=1,t=qd(null,t,l,e,n)):(t.tag=0,t=Cr(null,t,l,e,n));else{if(l!=null){if(u=l.$$typeof,u===V){t.tag=11,t=jd(null,t,l,e,n);break e}else if(u===F){t.tag=14,t=Hd(null,t,l,e,n);break e}}throw t=pe(l)||l,Error(r(306,t,""))}}return t;case 0:return Cr(e,t,t.type,t.pendingProps,n);case 1:return l=t.type,u=sl(l,t.pendingProps),qd(e,t,l,u,n);case 3:e:{if(Me(t,t.stateNode.containerInfo),e===null)throw Error(r(387));l=t.pendingProps;var i=t.memoizedState;u=i.element,ir(e,t),La(t,l,null,n);var f=t.memoizedState;if(l=f.cache,An(t,Xe,l),l!==i.cache&&er(t,[Xe],n,!0),Ba(),l=f.element,i.isDehydrated)if(i={element:l,isDehydrated:!1,cache:f.cache},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){t=kd(e,t,l,n);break e}else if(l!==u){u=Mt(Error(r(424)),t),Ca(u),t=kd(e,t,l,n);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Be=jt(e.firstChild),lt=t,Re=!0,al=null,kt=!0,n=wd(t,null,l,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(_a(),l===u){t=rn(e,t,n);break e}Fe(e,t,l,n)}t=t.child}return t;case 26:return mi(e,t),e===null?(n=Fm(t.type,null,t.pendingProps,null))?t.memoizedState=n:Re||(n=t.type,e=t.pendingProps,l=Oi(ae.current).createElement(n),l[Ie]=t,l[it]=e,Pe(l,n,e),Ze(l),t.stateNode=l):t.memoizedState=Fm(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return Lt(t),e===null&&Re&&(l=t.stateNode=Km(t.type,t.pendingProps,ae.current),lt=t,kt=!0,u=Be,Ln(t.type)?(pc=u,Be=jt(l.firstChild)):Be=u),Fe(e,t,t.pendingProps.children,n),mi(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&Re&&((u=l=Be)&&(l=l0(l,t.type,t.pendingProps,kt),l!==null?(t.stateNode=l,lt=t,Be=jt(l.firstChild),kt=!1,u=!0):u=!1),u||ul(t)),Lt(t),u=t.type,i=t.pendingProps,f=e!==null?e.memoizedProps:null,l=i.children,mc(u,i)?l=null:f!==null&&mc(u,f)&&(t.flags|=32),t.memoizedState!==null&&(u=dr(e,t,xy,null,null,n),iu._currentValue=u),mi(e,t),Fe(e,t,l,n),t.child;case 6:return e===null&&Re&&((e=n=Be)&&(n=a0(n,t.pendingProps,kt),n!==null?(t.stateNode=n,lt=t,Be=null,e=!0):e=!1),e||ul(t)),null;case 13:return Xd(e,t,n);case 4:return Me(t,t.stateNode.containerInfo),l=t.pendingProps,e===null?t.child=Xl(t,null,l,n):Fe(e,t,l,n),t.child;case 11:return jd(e,t,t.type,t.pendingProps,n);case 7:return Fe(e,t,t.pendingProps,n),t.child;case 8:return Fe(e,t,t.pendingProps.children,n),t.child;case 12:return Fe(e,t,t.pendingProps.children,n),t.child;case 10:return l=t.pendingProps,An(t,t.type,l.value),Fe(e,t,l.children,n),t.child;case 9:return u=t.type._context,l=t.pendingProps.children,ol(t),u=et(u),l=l(u),t.flags|=1,Fe(e,t,l,n),t.child;case 14:return Hd(e,t,t.type,t.pendingProps,n);case 15:return Bd(e,t,t.type,t.pendingProps,n);case 19:return Qd(e,t,n);case 31:return l=t.pendingProps,n=t.mode,l={mode:l.mode,children:l.children},e===null?(n=hi(l,n),n.ref=t.ref,t.child=n,n.return=t,t=n):(n=en(e.child,l),n.ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return Ld(e,t,n);case 24:return ol(t),l=et(Xe),e===null?(u=lr(),u===null&&(u=Ue,i=tr(),u.pooledCache=i,i.refCount++,i!==null&&(u.pooledCacheLanes|=n),u=i),t.memoizedState={parent:l,cache:u},ur(t),An(t,Xe,u)):((e.lanes&n)!==0&&(ir(e,t),La(t,null,null,n),Ba()),u=e.memoizedState,i=t.memoizedState,u.parent!==l?(u={parent:l,cache:l},t.memoizedState=u,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=u),An(t,Xe,l)):(l=i.cache,An(t,Xe,l),l!==u.cache&&er(t,[Xe],n,!0))),Fe(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(r(156,t.tag))}function cn(e){e.flags|=4}function Kd(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!th(t)){if(t=_t.current,t!==null&&((Ae&4194048)===Ae?Xt!==null:(Ae&62914560)!==Ae&&(Ae&536870912)===0||t!==Xt))throw ja=ar,Cf;e.flags|=8192}}function vi(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?ws():536870912,e.lanes|=t,Kl|=t)}function Qa(e,t){if(!Re)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var l=null;n!==null;)n.alternate!==null&&(l=n),n=n.sibling;l===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:l.sibling=null}}function He(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,l=0;if(t)for(var u=e.child;u!==null;)n|=u.lanes|u.childLanes,l|=u.subtreeFlags&65011712,l|=u.flags&65011712,u.return=e,u=u.sibling;else for(u=e.child;u!==null;)n|=u.lanes|u.childLanes,l|=u.subtreeFlags,l|=u.flags,u.return=e,u=u.sibling;return e.subtreeFlags|=l,e.childLanes=n,t}function Cy(e,t,n){var l=t.pendingProps;switch(Fo(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return He(t),null;case 1:return He(t),null;case 3:return n=t.stateNode,l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),an(Xe),nt(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&(Oa(t)?cn(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Mf())),He(t),null;case 26:return n=t.memoizedState,e===null?(cn(t),n!==null?(He(t),Kd(t,n)):(He(t),t.flags&=-16777217)):n?n!==e.memoizedState?(cn(t),He(t),Kd(t,n)):(He(t),t.flags&=-16777217):(e.memoizedProps!==l&&cn(t),He(t),t.flags&=-16777217),null;case 27:Gt(t),n=ae.current;var u=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==l&&cn(t);else{if(!l){if(t.stateNode===null)throw Error(r(166));return He(t),null}e=P.current,Oa(t)?Af(t):(e=Km(u,l,n),t.stateNode=e,cn(t))}return He(t),null;case 5:if(Gt(t),n=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==l&&cn(t);else{if(!l){if(t.stateNode===null)throw Error(r(166));return He(t),null}if(e=P.current,Oa(t))Af(t);else{switch(u=Oi(ae.current),e){case 1:e=u.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=u.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=u.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=u.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":e=u.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof l.is=="string"?u.createElement("select",{is:l.is}):u.createElement("select"),l.multiple?e.multiple=!0:l.size&&(e.size=l.size);break;default:e=typeof l.is=="string"?u.createElement(n,{is:l.is}):u.createElement(n)}}e[Ie]=t,e[it]=l;e:for(u=t.child;u!==null;){if(u.tag===5||u.tag===6)e.appendChild(u.stateNode);else if(u.tag!==4&&u.tag!==27&&u.child!==null){u.child.return=u,u=u.child;continue}if(u===t)break e;for(;u.sibling===null;){if(u.return===null||u.return===t)break e;u=u.return}u.sibling.return=u.return,u=u.sibling}t.stateNode=e;e:switch(Pe(e,n,l),n){case"button":case"input":case"select":case"textarea":e=!!l.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&cn(t)}}return He(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==l&&cn(t);else{if(typeof l!="string"&&t.stateNode===null)throw Error(r(166));if(e=ae.current,Oa(t)){if(e=t.stateNode,n=t.memoizedProps,l=null,u=lt,u!==null)switch(u.tag){case 27:case 5:l=u.memoizedProps}e[Ie]=t,e=!!(e.nodeValue===n||l!==null&&l.suppressHydrationWarning===!0||Ym(e.nodeValue,n)),e||ul(t)}else e=Oi(e).createTextNode(l),e[Ie]=t,t.stateNode=e}return He(t),null;case 13:if(l=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(u=Oa(t),l!==null&&l.dehydrated!==null){if(e===null){if(!u)throw Error(r(318));if(u=t.memoizedState,u=u!==null?u.dehydrated:null,!u)throw Error(r(317));u[Ie]=t}else _a(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;He(t),u=!1}else u=Mf(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=u),u=!0;if(!u)return t.flags&256?(on(t),t):(on(t),null)}if(on(t),(t.flags&128)!==0)return t.lanes=n,t;if(n=l!==null,e=e!==null&&e.memoizedState!==null,n){l=t.child,u=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(u=l.alternate.memoizedState.cachePool.pool);var i=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(i=l.memoizedState.cachePool.pool),i!==u&&(l.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),vi(t,t.updateQueue),He(t),null;case 4:return nt(),e===null&&rc(t.stateNode.containerInfo),He(t),null;case 10:return an(t.type),He(t),null;case 19:if($(Ve),u=t.memoizedState,u===null)return He(t),null;if(l=(t.flags&128)!==0,i=u.rendering,i===null)if(l)Qa(u,!1);else{if(Le!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(i=si(e),i!==null){for(t.flags|=128,Qa(u,!1),e=i.updateQueue,t.updateQueue=e,vi(t,e),t.subtreeFlags=0,e=n,n=t.child;n!==null;)xf(n,e),n=n.sibling;return K(Ve,Ve.current&1|2),t.child}e=e.sibling}u.tail!==null&&qt()>yi&&(t.flags|=128,l=!0,Qa(u,!1),t.lanes=4194304)}else{if(!l)if(e=si(i),e!==null){if(t.flags|=128,l=!0,e=e.updateQueue,t.updateQueue=e,vi(t,e),Qa(u,!0),u.tail===null&&u.tailMode==="hidden"&&!i.alternate&&!Re)return He(t),null}else 2*qt()-u.renderingStartTime>yi&&n!==536870912&&(t.flags|=128,l=!0,Qa(u,!1),t.lanes=4194304);u.isBackwards?(i.sibling=t.child,t.child=i):(e=u.last,e!==null?e.sibling=i:t.child=i,u.last=i)}return u.tail!==null?(t=u.tail,u.rendering=t,u.tail=t.sibling,u.renderingStartTime=qt(),t.sibling=null,e=Ve.current,K(Ve,l?e&1|2:e&1),t):(He(t),null);case 22:case 23:return on(t),sr(),l=t.memoizedState!==null,e!==null?e.memoizedState!==null!==l&&(t.flags|=8192):l&&(t.flags|=8192),l?(n&536870912)!==0&&(t.flags&128)===0&&(He(t),t.subtreeFlags&6&&(t.flags|=8192)):He(t),n=t.updateQueue,n!==null&&vi(t,n.retryQueue),n=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),l=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),l!==n&&(t.flags|=2048),e!==null&&$(rl),null;case 24:return n=null,e!==null&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),an(Xe),He(t),null;case 25:return null;case 30:return null}throw Error(r(156,t.tag))}function Dy(e,t){switch(Fo(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return an(Xe),nt(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return Gt(t),null;case 13:if(on(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(r(340));_a()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return $(Ve),null;case 4:return nt(),null;case 10:return an(t.type),null;case 22:case 23:return on(t),sr(),e!==null&&$(rl),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return an(Xe),null;case 25:return null;default:return null}}function Jd(e,t){switch(Fo(t),t.tag){case 3:an(Xe),nt();break;case 26:case 27:case 5:Gt(t);break;case 4:nt();break;case 13:on(t);break;case 19:$(Ve);break;case 10:an(t.type);break;case 22:case 23:on(t),sr(),e!==null&&$(rl);break;case 24:an(Xe)}}function Za(e,t){try{var n=t.updateQueue,l=n!==null?n.lastEffect:null;if(l!==null){var u=l.next;n=u;do{if((n.tag&e)===e){l=void 0;var i=n.create,f=n.inst;l=i(),f.destroy=l}n=n.next}while(n!==u)}}catch(m){ze(t,t.return,m)}}function Cn(e,t,n){try{var l=t.updateQueue,u=l!==null?l.lastEffect:null;if(u!==null){var i=u.next;l=i;do{if((l.tag&e)===e){var f=l.inst,m=f.destroy;if(m!==void 0){f.destroy=void 0,u=t;var S=n,_=m;try{_()}catch(B){ze(u,S,B)}}}l=l.next}while(l!==i)}}catch(B){ze(t,t.return,B)}}function Wd(e){var t=e.updateQueue;if(t!==null){var n=e.stateNode;try{Hf(t,n)}catch(l){ze(e,e.return,l)}}}function Fd(e,t,n){n.props=sl(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(l){ze(e,t,l)}}function Ka(e,t){try{var n=e.ref;if(n!==null){switch(e.tag){case 26:case 27:case 5:var l=e.stateNode;break;case 30:l=e.stateNode;break;default:l=e.stateNode}typeof n=="function"?e.refCleanup=n(l):n.current=l}}catch(u){ze(e,t,u)}}function Vt(e,t){var n=e.ref,l=e.refCleanup;if(n!==null)if(typeof l=="function")try{l()}catch(u){ze(e,t,u)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(u){ze(e,t,u)}else n.current=null}function $d(e){var t=e.type,n=e.memoizedProps,l=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&l.focus();break e;case"img":n.src?l.src=n.src:n.srcSet&&(l.srcset=n.srcSet)}}catch(u){ze(e,e.return,u)}}function Lr(e,t,n){try{var l=e.stateNode;Py(l,e.type,n,t),l[it]=t}catch(u){ze(e,e.return,u)}}function Pd(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Ln(e.type)||e.tag===4}function Gr(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Pd(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Ln(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Yr(e,t,n){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(e,t):(t=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,t.appendChild(e),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Ri));else if(l!==4&&(l===27&&Ln(e.type)&&(n=e.stateNode,t=null),e=e.child,e!==null))for(Yr(e,t,n),e=e.sibling;e!==null;)Yr(e,t,n),e=e.sibling}function gi(e,t,n){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(l!==4&&(l===27&&Ln(e.type)&&(n=e.stateNode),e=e.child,e!==null))for(gi(e,t,n),e=e.sibling;e!==null;)gi(e,t,n),e=e.sibling}function Id(e){var t=e.stateNode,n=e.memoizedProps;try{for(var l=e.type,u=t.attributes;u.length;)t.removeAttributeNode(u[0]);Pe(t,l,n),t[Ie]=e,t[it]=n}catch(i){ze(e,e.return,i)}}var sn=!1,Ye=!1,qr=!1,em=typeof WeakSet=="function"?WeakSet:Set,Je=null;function Ny(e,t){if(e=e.containerInfo,fc=Ui,e=ff(e),Go(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var l=n.getSelection&&n.getSelection();if(l&&l.rangeCount!==0){n=l.anchorNode;var u=l.anchorOffset,i=l.focusNode;l=l.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var f=0,m=-1,S=-1,_=0,B=0,q=e,D=null;t:for(;;){for(var N;q!==n||u!==0&&q.nodeType!==3||(m=f+u),q!==i||l!==0&&q.nodeType!==3||(S=f+l),q.nodeType===3&&(f+=q.nodeValue.length),(N=q.firstChild)!==null;)D=q,q=N;for(;;){if(q===e)break t;if(D===n&&++_===u&&(m=f),D===i&&++B===l&&(S=f),(N=q.nextSibling)!==null)break;q=D,D=q.parentNode}q=N}n=m===-1||S===-1?null:{start:m,end:S}}else n=null}n=n||{start:0,end:0}}else n=null;for(dc={focusedElem:e,selectionRange:n},Ui=!1,Je=t;Je!==null;)if(t=Je,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,Je=e;else for(;Je!==null;){switch(t=Je,i=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&i!==null){e=void 0,n=t,u=i.memoizedProps,i=i.memoizedState,l=n.stateNode;try{var ce=sl(n.type,u,n.elementType===n.type);e=l.getSnapshotBeforeUpdate(ce,i),l.__reactInternalSnapshotBeforeUpdate=e}catch(ie){ze(n,n.return,ie)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,n=e.nodeType,n===9)vc(e);else if(n===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":vc(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(r(163))}if(e=t.sibling,e!==null){e.return=t.return,Je=e;break}Je=t.return}}function tm(e,t,n){var l=n.flags;switch(n.tag){case 0:case 11:case 15:Dn(e,n),l&4&&Za(5,n);break;case 1:if(Dn(e,n),l&4)if(e=n.stateNode,t===null)try{e.componentDidMount()}catch(f){ze(n,n.return,f)}else{var u=sl(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(u,t,e.__reactInternalSnapshotBeforeUpdate)}catch(f){ze(n,n.return,f)}}l&64&&Wd(n),l&512&&Ka(n,n.return);break;case 3:if(Dn(e,n),l&64&&(e=n.updateQueue,e!==null)){if(t=null,n.child!==null)switch(n.child.tag){case 27:case 5:t=n.child.stateNode;break;case 1:t=n.child.stateNode}try{Hf(e,t)}catch(f){ze(n,n.return,f)}}break;case 27:t===null&&l&4&&Id(n);case 26:case 5:Dn(e,n),t===null&&l&4&&$d(n),l&512&&Ka(n,n.return);break;case 12:Dn(e,n);break;case 13:Dn(e,n),l&4&&am(e,n),l&64&&(e=n.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(n=qy.bind(null,n),u0(e,n))));break;case 22:if(l=n.memoizedState!==null||sn,!l){t=t!==null&&t.memoizedState!==null||Ye,u=sn;var i=Ye;sn=l,(Ye=t)&&!i?Nn(e,n,(n.subtreeFlags&8772)!==0):Dn(e,n),sn=u,Ye=i}break;case 30:break;default:Dn(e,n)}}function nm(e){var t=e.alternate;t!==null&&(e.alternate=null,nm(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&So(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var je=null,ct=!1;function fn(e,t,n){for(n=n.child;n!==null;)lm(e,t,n),n=n.sibling}function lm(e,t,n){if(dt&&typeof dt.onCommitFiberUnmount=="function")try{dt.onCommitFiberUnmount(ha,n)}catch{}switch(n.tag){case 26:Ye||Vt(n,t),fn(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:Ye||Vt(n,t);var l=je,u=ct;Ln(n.type)&&(je=n.stateNode,ct=!1),fn(e,t,n),nu(n.stateNode),je=l,ct=u;break;case 5:Ye||Vt(n,t);case 6:if(l=je,u=ct,je=null,fn(e,t,n),je=l,ct=u,je!==null)if(ct)try{(je.nodeType===9?je.body:je.nodeName==="HTML"?je.ownerDocument.body:je).removeChild(n.stateNode)}catch(i){ze(n,t,i)}else try{je.removeChild(n.stateNode)}catch(i){ze(n,t,i)}break;case 18:je!==null&&(ct?(e=je,Qm(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,n.stateNode),su(e)):Qm(je,n.stateNode));break;case 4:l=je,u=ct,je=n.stateNode.containerInfo,ct=!0,fn(e,t,n),je=l,ct=u;break;case 0:case 11:case 14:case 15:Ye||Cn(2,n,t),Ye||Cn(4,n,t),fn(e,t,n);break;case 1:Ye||(Vt(n,t),l=n.stateNode,typeof l.componentWillUnmount=="function"&&Fd(n,t,l)),fn(e,t,n);break;case 21:fn(e,t,n);break;case 22:Ye=(l=Ye)||n.memoizedState!==null,fn(e,t,n),Ye=l;break;default:fn(e,t,n)}}function am(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{su(e)}catch(n){ze(t,t.return,n)}}function zy(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new em),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new em),t;default:throw Error(r(435,e.tag))}}function kr(e,t){var n=zy(e);t.forEach(function(l){var u=ky.bind(null,e,l);n.has(l)||(n.add(l),l.then(u,u))})}function gt(e,t){var n=t.deletions;if(n!==null)for(var l=0;l<n.length;l++){var u=n[l],i=e,f=t,m=f;e:for(;m!==null;){switch(m.tag){case 27:if(Ln(m.type)){je=m.stateNode,ct=!1;break e}break;case 5:je=m.stateNode,ct=!1;break e;case 3:case 4:je=m.stateNode.containerInfo,ct=!0;break e}m=m.return}if(je===null)throw Error(r(160));lm(i,f,u),je=null,ct=!1,i=u.alternate,i!==null&&(i.return=null),u.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)um(t,e),t=t.sibling}var Ut=null;function um(e,t){var n=e.alternate,l=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:gt(t,e),pt(e),l&4&&(Cn(3,e,e.return),Za(3,e),Cn(5,e,e.return));break;case 1:gt(t,e),pt(e),l&512&&(Ye||n===null||Vt(n,n.return)),l&64&&sn&&(e=e.updateQueue,e!==null&&(l=e.callbacks,l!==null&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=n===null?l:n.concat(l))));break;case 26:var u=Ut;if(gt(t,e),pt(e),l&512&&(Ye||n===null||Vt(n,n.return)),l&4){var i=n!==null?n.memoizedState:null;if(l=e.memoizedState,n===null)if(l===null)if(e.stateNode===null){e:{l=e.type,n=e.memoizedProps,u=u.ownerDocument||u;t:switch(l){case"title":i=u.getElementsByTagName("title")[0],(!i||i[pa]||i[Ie]||i.namespaceURI==="http://www.w3.org/2000/svg"||i.hasAttribute("itemprop"))&&(i=u.createElement(l),u.head.insertBefore(i,u.querySelector("head > title"))),Pe(i,l,n),i[Ie]=e,Ze(i),l=i;break e;case"link":var f=Im("link","href",u).get(l+(n.href||""));if(f){for(var m=0;m<f.length;m++)if(i=f[m],i.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&i.getAttribute("rel")===(n.rel==null?null:n.rel)&&i.getAttribute("title")===(n.title==null?null:n.title)&&i.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){f.splice(m,1);break t}}i=u.createElement(l),Pe(i,l,n),u.head.appendChild(i);break;case"meta":if(f=Im("meta","content",u).get(l+(n.content||""))){for(m=0;m<f.length;m++)if(i=f[m],i.getAttribute("content")===(n.content==null?null:""+n.content)&&i.getAttribute("name")===(n.name==null?null:n.name)&&i.getAttribute("property")===(n.property==null?null:n.property)&&i.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&i.getAttribute("charset")===(n.charSet==null?null:n.charSet)){f.splice(m,1);break t}}i=u.createElement(l),Pe(i,l,n),u.head.appendChild(i);break;default:throw Error(r(468,l))}i[Ie]=e,Ze(i),l=i}e.stateNode=l}else eh(u,e.type,e.stateNode);else e.stateNode=Pm(u,l,e.memoizedProps);else i!==l?(i===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):i.count--,l===null?eh(u,e.type,e.stateNode):Pm(u,l,e.memoizedProps)):l===null&&e.stateNode!==null&&Lr(e,e.memoizedProps,n.memoizedProps)}break;case 27:gt(t,e),pt(e),l&512&&(Ye||n===null||Vt(n,n.return)),n!==null&&l&4&&Lr(e,e.memoizedProps,n.memoizedProps);break;case 5:if(gt(t,e),pt(e),l&512&&(Ye||n===null||Vt(n,n.return)),e.flags&32){u=e.stateNode;try{Ml(u,"")}catch(N){ze(e,e.return,N)}}l&4&&e.stateNode!=null&&(u=e.memoizedProps,Lr(e,u,n!==null?n.memoizedProps:u)),l&1024&&(qr=!0);break;case 6:if(gt(t,e),pt(e),l&4){if(e.stateNode===null)throw Error(r(162));l=e.memoizedProps,n=e.stateNode;try{n.nodeValue=l}catch(N){ze(e,e.return,N)}}break;case 3:if(Di=null,u=Ut,Ut=_i(t.containerInfo),gt(t,e),Ut=u,pt(e),l&4&&n!==null&&n.memoizedState.isDehydrated)try{su(t.containerInfo)}catch(N){ze(e,e.return,N)}qr&&(qr=!1,im(e));break;case 4:l=Ut,Ut=_i(e.stateNode.containerInfo),gt(t,e),pt(e),Ut=l;break;case 12:gt(t,e),pt(e);break;case 13:gt(t,e),pt(e),e.child.flags&8192&&e.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(Jr=qt()),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,kr(e,l)));break;case 22:u=e.memoizedState!==null;var S=n!==null&&n.memoizedState!==null,_=sn,B=Ye;if(sn=_||u,Ye=B||S,gt(t,e),Ye=B,sn=_,pt(e),l&8192)e:for(t=e.stateNode,t._visibility=u?t._visibility&-2:t._visibility|1,u&&(n===null||S||sn||Ye||fl(e)),n=null,t=e;;){if(t.tag===5||t.tag===26){if(n===null){S=n=t;try{if(i=S.stateNode,u)f=i.style,typeof f.setProperty=="function"?f.setProperty("display","none","important"):f.display="none";else{m=S.stateNode;var q=S.memoizedProps.style,D=q!=null&&q.hasOwnProperty("display")?q.display:null;m.style.display=D==null||typeof D=="boolean"?"":(""+D).trim()}}catch(N){ze(S,S.return,N)}}}else if(t.tag===6){if(n===null){S=t;try{S.stateNode.nodeValue=u?"":S.memoizedProps}catch(N){ze(S,S.return,N)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}l&4&&(l=e.updateQueue,l!==null&&(n=l.retryQueue,n!==null&&(l.retryQueue=null,kr(e,n))));break;case 19:gt(t,e),pt(e),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,kr(e,l)));break;case 30:break;case 21:break;default:gt(t,e),pt(e)}}function pt(e){var t=e.flags;if(t&2){try{for(var n,l=e.return;l!==null;){if(Pd(l)){n=l;break}l=l.return}if(n==null)throw Error(r(160));switch(n.tag){case 27:var u=n.stateNode,i=Gr(e);gi(e,i,u);break;case 5:var f=n.stateNode;n.flags&32&&(Ml(f,""),n.flags&=-33);var m=Gr(e);gi(e,m,f);break;case 3:case 4:var S=n.stateNode.containerInfo,_=Gr(e);Yr(e,_,S);break;default:throw Error(r(161))}}catch(B){ze(e,e.return,B)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function im(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;im(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function Dn(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)tm(e,t.alternate,t),t=t.sibling}function fl(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:Cn(4,t,t.return),fl(t);break;case 1:Vt(t,t.return);var n=t.stateNode;typeof n.componentWillUnmount=="function"&&Fd(t,t.return,n),fl(t);break;case 27:nu(t.stateNode);case 26:case 5:Vt(t,t.return),fl(t);break;case 22:t.memoizedState===null&&fl(t);break;case 30:fl(t);break;default:fl(t)}e=e.sibling}}function Nn(e,t,n){for(n=n&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var l=t.alternate,u=e,i=t,f=i.flags;switch(i.tag){case 0:case 11:case 15:Nn(u,i,n),Za(4,i);break;case 1:if(Nn(u,i,n),l=i,u=l.stateNode,typeof u.componentDidMount=="function")try{u.componentDidMount()}catch(_){ze(l,l.return,_)}if(l=i,u=l.updateQueue,u!==null){var m=l.stateNode;try{var S=u.shared.hiddenCallbacks;if(S!==null)for(u.shared.hiddenCallbacks=null,u=0;u<S.length;u++)jf(S[u],m)}catch(_){ze(l,l.return,_)}}n&&f&64&&Wd(i),Ka(i,i.return);break;case 27:Id(i);case 26:case 5:Nn(u,i,n),n&&l===null&&f&4&&$d(i),Ka(i,i.return);break;case 12:Nn(u,i,n);break;case 13:Nn(u,i,n),n&&f&4&&am(u,i);break;case 22:i.memoizedState===null&&Nn(u,i,n),Ka(i,i.return);break;case 30:break;default:Nn(u,i,n)}t=t.sibling}}function Xr(e,t){var n=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==n&&(e!=null&&e.refCount++,n!=null&&Na(n))}function Vr(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Na(e))}function Qt(e,t,n,l){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)om(e,t,n,l),t=t.sibling}function om(e,t,n,l){var u=t.flags;switch(t.tag){case 0:case 11:case 15:Qt(e,t,n,l),u&2048&&Za(9,t);break;case 1:Qt(e,t,n,l);break;case 3:Qt(e,t,n,l),u&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Na(e)));break;case 12:if(u&2048){Qt(e,t,n,l),e=t.stateNode;try{var i=t.memoizedProps,f=i.id,m=i.onPostCommit;typeof m=="function"&&m(f,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(S){ze(t,t.return,S)}}else Qt(e,t,n,l);break;case 13:Qt(e,t,n,l);break;case 23:break;case 22:i=t.stateNode,f=t.alternate,t.memoizedState!==null?i._visibility&2?Qt(e,t,n,l):Ja(e,t):i._visibility&2?Qt(e,t,n,l):(i._visibility|=2,Vl(e,t,n,l,(t.subtreeFlags&10256)!==0)),u&2048&&Xr(f,t);break;case 24:Qt(e,t,n,l),u&2048&&Vr(t.alternate,t);break;default:Qt(e,t,n,l)}}function Vl(e,t,n,l,u){for(u=u&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var i=e,f=t,m=n,S=l,_=f.flags;switch(f.tag){case 0:case 11:case 15:Vl(i,f,m,S,u),Za(8,f);break;case 23:break;case 22:var B=f.stateNode;f.memoizedState!==null?B._visibility&2?Vl(i,f,m,S,u):Ja(i,f):(B._visibility|=2,Vl(i,f,m,S,u)),u&&_&2048&&Xr(f.alternate,f);break;case 24:Vl(i,f,m,S,u),u&&_&2048&&Vr(f.alternate,f);break;default:Vl(i,f,m,S,u)}t=t.sibling}}function Ja(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var n=e,l=t,u=l.flags;switch(l.tag){case 22:Ja(n,l),u&2048&&Xr(l.alternate,l);break;case 24:Ja(n,l),u&2048&&Vr(l.alternate,l);break;default:Ja(n,l)}t=t.sibling}}var Wa=8192;function Ql(e){if(e.subtreeFlags&Wa)for(e=e.child;e!==null;)rm(e),e=e.sibling}function rm(e){switch(e.tag){case 26:Ql(e),e.flags&Wa&&e.memoizedState!==null&&y0(Ut,e.memoizedState,e.memoizedProps);break;case 5:Ql(e);break;case 3:case 4:var t=Ut;Ut=_i(e.stateNode.containerInfo),Ql(e),Ut=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=Wa,Wa=16777216,Ql(e),Wa=t):Ql(e));break;default:Ql(e)}}function cm(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function Fa(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var l=t[n];Je=l,fm(l,e)}cm(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)sm(e),e=e.sibling}function sm(e){switch(e.tag){case 0:case 11:case 15:Fa(e),e.flags&2048&&Cn(9,e,e.return);break;case 3:Fa(e);break;case 12:Fa(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,pi(e)):Fa(e);break;default:Fa(e)}}function pi(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var l=t[n];Je=l,fm(l,e)}cm(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:Cn(8,t,t.return),pi(t);break;case 22:n=t.stateNode,n._visibility&2&&(n._visibility&=-3,pi(t));break;default:pi(t)}e=e.sibling}}function fm(e,t){for(;Je!==null;){var n=Je;switch(n.tag){case 0:case 11:case 15:Cn(8,n,t);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var l=n.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:Na(n.memoizedState.cache)}if(l=n.child,l!==null)l.return=n,Je=l;else e:for(n=e;Je!==null;){l=Je;var u=l.sibling,i=l.return;if(nm(l),l===n){Je=null;break e}if(u!==null){u.return=i,Je=u;break e}Je=i}}}var Uy={getCacheForType:function(e){var t=et(Xe),n=t.data.get(e);return n===void 0&&(n=e(),t.data.set(e,n)),n}},jy=typeof WeakMap=="function"?WeakMap:Map,Oe=0,Ue=null,be=null,Ae=0,_e=0,yt=null,zn=!1,Zl=!1,Qr=!1,dn=0,Le=0,Un=0,dl=0,Zr=0,Ct=0,Kl=0,$a=null,st=null,Kr=!1,Jr=0,yi=1/0,bi=null,jn=null,$e=0,Hn=null,Jl=null,Wl=0,Wr=0,Fr=null,dm=null,Pa=0,$r=null;function bt(){if((Oe&2)!==0&&Ae!==0)return Ae&-Ae;if(O.T!==null){var e=Hl;return e!==0?e:ac()}return Rs()}function mm(){Ct===0&&(Ct=(Ae&536870912)===0||Re?As():536870912);var e=_t.current;return e!==null&&(e.flags|=32),Ct}function St(e,t,n){(e===Ue&&(_e===2||_e===9)||e.cancelPendingCommit!==null)&&(Fl(e,0),Bn(e,Ae,Ct,!1)),ga(e,n),((Oe&2)===0||e!==Ue)&&(e===Ue&&((Oe&2)===0&&(dl|=n),Le===4&&Bn(e,Ae,Ct,!1)),Zt(e))}function hm(e,t,n){if((Oe&6)!==0)throw Error(r(327));var l=!n&&(t&124)===0&&(t&e.expiredLanes)===0||va(e,t),u=l?Ly(e,t):ec(e,t,!0),i=l;do{if(u===0){Zl&&!l&&Bn(e,t,0,!1);break}else{if(n=e.current.alternate,i&&!Hy(n)){u=ec(e,t,!1),i=!1;continue}if(u===2){if(i=t,e.errorRecoveryDisabledLanes&i)var f=0;else f=e.pendingLanes&-536870913,f=f!==0?f:f&536870912?536870912:0;if(f!==0){t=f;e:{var m=e;u=$a;var S=m.current.memoizedState.isDehydrated;if(S&&(Fl(m,f).flags|=256),f=ec(m,f,!1),f!==2){if(Qr&&!S){m.errorRecoveryDisabledLanes|=i,dl|=i,u=4;break e}i=st,st=u,i!==null&&(st===null?st=i:st.push.apply(st,i))}u=f}if(i=!1,u!==2)continue}}if(u===1){Fl(e,0),Bn(e,t,0,!0);break}e:{switch(l=e,i=u,i){case 0:case 1:throw Error(r(345));case 4:if((t&4194048)!==t)break;case 6:Bn(l,t,Ct,!zn);break e;case 2:st=null;break;case 3:case 5:break;default:throw Error(r(329))}if((t&62914560)===t&&(u=Jr+300-qt(),10<u)){if(Bn(l,t,Ct,!zn),Cu(l,0,!0)!==0)break e;l.timeoutHandle=Xm(vm.bind(null,l,n,st,bi,Kr,t,Ct,dl,Kl,zn,i,2,-0,0),u);break e}vm(l,n,st,bi,Kr,t,Ct,dl,Kl,zn,i,0,-0,0)}}break}while(!0);Zt(e)}function vm(e,t,n,l,u,i,f,m,S,_,B,q,D,N){if(e.timeoutHandle=-1,q=t.subtreeFlags,(q&8192||(q&16785408)===16785408)&&(uu={stylesheets:null,count:0,unsuspend:p0},rm(t),q=b0(),q!==null)){e.cancelPendingCommit=q(Em.bind(null,e,t,i,n,l,u,f,m,S,B,1,D,N)),Bn(e,i,f,!_);return}Em(e,t,i,n,l,u,f,m,S)}function Hy(e){for(var t=e;;){var n=t.tag;if((n===0||n===11||n===15)&&t.flags&16384&&(n=t.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var l=0;l<n.length;l++){var u=n[l],i=u.getSnapshot;u=u.value;try{if(!ht(i(),u))return!1}catch{return!1}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Bn(e,t,n,l){t&=~Zr,t&=~dl,e.suspendedLanes|=t,e.pingedLanes&=~t,l&&(e.warmLanes|=t),l=e.expirationTimes;for(var u=t;0<u;){var i=31-mt(u),f=1<<i;l[i]=-1,u&=~f}n!==0&&Ms(e,n,t)}function Si(){return(Oe&6)===0?(Ia(0),!1):!0}function Pr(){if(be!==null){if(_e===0)var e=be.return;else e=be,ln=il=null,vr(e),kl=null,Xa=0,e=be;for(;e!==null;)Jd(e.alternate,e),e=e.return;be=null}}function Fl(e,t){var n=e.timeoutHandle;n!==-1&&(e.timeoutHandle=-1,e0(n)),n=e.cancelPendingCommit,n!==null&&(e.cancelPendingCommit=null,n()),Pr(),Ue=e,be=n=en(e.current,null),Ae=t,_e=0,yt=null,zn=!1,Zl=va(e,t),Qr=!1,Kl=Ct=Zr=dl=Un=Le=0,st=$a=null,Kr=!1,(t&8)!==0&&(t|=t&32);var l=e.entangledLanes;if(l!==0)for(e=e.entanglements,l&=t;0<l;){var u=31-mt(l),i=1<<u;t|=e[u],l&=~i}return dn=t,ku(),n}function gm(e,t){ve=null,O.H=oi,t===Ua||t===$u?(t=zf(),_e=3):t===Cf?(t=zf(),_e=4):_e=t===Ud?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,yt=t,be===null&&(Le=1,di(e,Mt(t,e.current)))}function pm(){var e=O.H;return O.H=oi,e===null?oi:e}function ym(){var e=O.A;return O.A=Uy,e}function Ir(){Le=4,zn||(Ae&4194048)!==Ae&&_t.current!==null||(Zl=!0),(Un&134217727)===0&&(dl&134217727)===0||Ue===null||Bn(Ue,Ae,Ct,!1)}function ec(e,t,n){var l=Oe;Oe|=2;var u=pm(),i=ym();(Ue!==e||Ae!==t)&&(bi=null,Fl(e,t)),t=!1;var f=Le;e:do try{if(_e!==0&&be!==null){var m=be,S=yt;switch(_e){case 8:Pr(),f=6;break e;case 3:case 2:case 9:case 6:_t.current===null&&(t=!0);var _=_e;if(_e=0,yt=null,$l(e,m,S,_),n&&Zl){f=0;break e}break;default:_=_e,_e=0,yt=null,$l(e,m,S,_)}}By(),f=Le;break}catch(B){gm(e,B)}while(!0);return t&&e.shellSuspendCounter++,ln=il=null,Oe=l,O.H=u,O.A=i,be===null&&(Ue=null,Ae=0,ku()),f}function By(){for(;be!==null;)bm(be)}function Ly(e,t){var n=Oe;Oe|=2;var l=pm(),u=ym();Ue!==e||Ae!==t?(bi=null,yi=qt()+500,Fl(e,t)):Zl=va(e,t);e:do try{if(_e!==0&&be!==null){t=be;var i=yt;t:switch(_e){case 1:_e=0,yt=null,$l(e,t,i,1);break;case 2:case 9:if(Df(i)){_e=0,yt=null,Sm(t);break}t=function(){_e!==2&&_e!==9||Ue!==e||(_e=7),Zt(e)},i.then(t,t);break e;case 3:_e=7;break e;case 4:_e=5;break e;case 7:Df(i)?(_e=0,yt=null,Sm(t)):(_e=0,yt=null,$l(e,t,i,7));break;case 5:var f=null;switch(be.tag){case 26:f=be.memoizedState;case 5:case 27:var m=be;if(!f||th(f)){_e=0,yt=null;var S=m.sibling;if(S!==null)be=S;else{var _=m.return;_!==null?(be=_,xi(_)):be=null}break t}}_e=0,yt=null,$l(e,t,i,5);break;case 6:_e=0,yt=null,$l(e,t,i,6);break;case 8:Pr(),Le=6;break e;default:throw Error(r(462))}}Gy();break}catch(B){gm(e,B)}while(!0);return ln=il=null,O.H=l,O.A=u,Oe=n,be!==null?0:(Ue=null,Ae=0,ku(),Le)}function Gy(){for(;be!==null&&!op();)bm(be)}function bm(e){var t=Zd(e.alternate,e,dn);e.memoizedProps=e.pendingProps,t===null?xi(e):be=t}function Sm(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=Yd(n,t,t.pendingProps,t.type,void 0,Ae);break;case 11:t=Yd(n,t,t.pendingProps,t.type.render,t.ref,Ae);break;case 5:vr(t);default:Jd(n,t),t=be=xf(t,dn),t=Zd(n,t,dn)}e.memoizedProps=e.pendingProps,t===null?xi(e):be=t}function $l(e,t,n,l){ln=il=null,vr(t),kl=null,Xa=0;var u=t.return;try{if(Oy(e,u,t,n,Ae)){Le=1,di(e,Mt(n,e.current)),be=null;return}}catch(i){if(u!==null)throw be=u,i;Le=1,di(e,Mt(n,e.current)),be=null;return}t.flags&32768?(Re||l===1?e=!0:Zl||(Ae&536870912)!==0?e=!1:(zn=e=!0,(l===2||l===9||l===3||l===6)&&(l=_t.current,l!==null&&l.tag===13&&(l.flags|=16384))),xm(t,e)):xi(t)}function xi(e){var t=e;do{if((t.flags&32768)!==0){xm(t,zn);return}e=t.return;var n=Cy(t.alternate,t,dn);if(n!==null){be=n;return}if(t=t.sibling,t!==null){be=t;return}be=t=e}while(t!==null);Le===0&&(Le=5)}function xm(e,t){do{var n=Dy(e.alternate,e);if(n!==null){n.flags&=32767,be=n;return}if(n=e.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&(e=e.sibling,e!==null)){be=e;return}be=e=n}while(e!==null);Le=6,be=null}function Em(e,t,n,l,u,i,f,m,S){e.cancelPendingCommit=null;do Ei();while($e!==0);if((Oe&6)!==0)throw Error(r(327));if(t!==null){if(t===e.current)throw Error(r(177));if(i=t.lanes|t.childLanes,i|=Vo,pp(e,n,i,f,m,S),e===Ue&&(be=Ue=null,Ae=0),Jl=t,Hn=e,Wl=n,Wr=i,Fr=u,dm=l,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,Xy(Ru,function(){return Rm(),null})):(e.callbackNode=null,e.callbackPriority=0),l=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||l){l=O.T,O.T=null,u=Q.p,Q.p=2,f=Oe,Oe|=4;try{Ny(e,t,n)}finally{Oe=f,Q.p=u,O.T=l}}$e=1,Am(),wm(),Mm()}}function Am(){if($e===1){$e=0;var e=Hn,t=Jl,n=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||n){n=O.T,O.T=null;var l=Q.p;Q.p=2;var u=Oe;Oe|=4;try{um(t,e);var i=dc,f=ff(e.containerInfo),m=i.focusedElem,S=i.selectionRange;if(f!==m&&m&&m.ownerDocument&&sf(m.ownerDocument.documentElement,m)){if(S!==null&&Go(m)){var _=S.start,B=S.end;if(B===void 0&&(B=_),"selectionStart"in m)m.selectionStart=_,m.selectionEnd=Math.min(B,m.value.length);else{var q=m.ownerDocument||document,D=q&&q.defaultView||window;if(D.getSelection){var N=D.getSelection(),ce=m.textContent.length,ie=Math.min(S.start,ce),Ne=S.end===void 0?ie:Math.min(S.end,ce);!N.extend&&ie>Ne&&(f=Ne,Ne=ie,ie=f);var M=cf(m,ie),A=cf(m,Ne);if(M&&A&&(N.rangeCount!==1||N.anchorNode!==M.node||N.anchorOffset!==M.offset||N.focusNode!==A.node||N.focusOffset!==A.offset)){var R=q.createRange();R.setStart(M.node,M.offset),N.removeAllRanges(),ie>Ne?(N.addRange(R),N.extend(A.node,A.offset)):(R.setEnd(A.node,A.offset),N.addRange(R))}}}}for(q=[],N=m;N=N.parentNode;)N.nodeType===1&&q.push({element:N,left:N.scrollLeft,top:N.scrollTop});for(typeof m.focus=="function"&&m.focus(),m=0;m<q.length;m++){var L=q[m];L.element.scrollLeft=L.left,L.element.scrollTop=L.top}}Ui=!!fc,dc=fc=null}finally{Oe=u,Q.p=l,O.T=n}}e.current=t,$e=2}}function wm(){if($e===2){$e=0;var e=Hn,t=Jl,n=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||n){n=O.T,O.T=null;var l=Q.p;Q.p=2;var u=Oe;Oe|=4;try{tm(e,t.alternate,t)}finally{Oe=u,Q.p=l,O.T=n}}$e=3}}function Mm(){if($e===4||$e===3){$e=0,rp();var e=Hn,t=Jl,n=Wl,l=dm;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?$e=5:($e=0,Jl=Hn=null,Tm(e,e.pendingLanes));var u=e.pendingLanes;if(u===0&&(jn=null),yo(n),t=t.stateNode,dt&&typeof dt.onCommitFiberRoot=="function")try{dt.onCommitFiberRoot(ha,t,void 0,(t.current.flags&128)===128)}catch{}if(l!==null){t=O.T,u=Q.p,Q.p=2,O.T=null;try{for(var i=e.onRecoverableError,f=0;f<l.length;f++){var m=l[f];i(m.value,{componentStack:m.stack})}}finally{O.T=t,Q.p=u}}(Wl&3)!==0&&Ei(),Zt(e),u=e.pendingLanes,(n&4194090)!==0&&(u&42)!==0?e===$r?Pa++:(Pa=0,$r=e):Pa=0,Ia(0)}}function Tm(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,Na(t)))}function Ei(e){return Am(),wm(),Mm(),Rm()}function Rm(){if($e!==5)return!1;var e=Hn,t=Wr;Wr=0;var n=yo(Wl),l=O.T,u=Q.p;try{Q.p=32>n?32:n,O.T=null,n=Fr,Fr=null;var i=Hn,f=Wl;if($e=0,Jl=Hn=null,Wl=0,(Oe&6)!==0)throw Error(r(331));var m=Oe;if(Oe|=4,sm(i.current),om(i,i.current,f,n),Oe=m,Ia(0,!1),dt&&typeof dt.onPostCommitFiberRoot=="function")try{dt.onPostCommitFiberRoot(ha,i)}catch{}return!0}finally{Q.p=u,O.T=l,Tm(e,t)}}function Om(e,t,n){t=Mt(n,t),t=_r(e.stateNode,t,2),e=Tn(e,t,2),e!==null&&(ga(e,2),Zt(e))}function ze(e,t,n){if(e.tag===3)Om(e,e,n);else for(;t!==null;){if(t.tag===3){Om(t,e,n);break}else if(t.tag===1){var l=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(jn===null||!jn.has(l))){e=Mt(n,e),n=Nd(2),l=Tn(t,n,2),l!==null&&(zd(n,l,t,e),ga(l,2),Zt(l));break}}t=t.return}}function tc(e,t,n){var l=e.pingCache;if(l===null){l=e.pingCache=new jy;var u=new Set;l.set(t,u)}else u=l.get(t),u===void 0&&(u=new Set,l.set(t,u));u.has(n)||(Qr=!0,u.add(n),e=Yy.bind(null,e,t,n),t.then(e,e))}function Yy(e,t,n){var l=e.pingCache;l!==null&&l.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,Ue===e&&(Ae&n)===n&&(Le===4||Le===3&&(Ae&62914560)===Ae&&300>qt()-Jr?(Oe&2)===0&&Fl(e,0):Zr|=n,Kl===Ae&&(Kl=0)),Zt(e)}function _m(e,t){t===0&&(t=ws()),e=Nl(e,t),e!==null&&(ga(e,t),Zt(e))}function qy(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),_m(e,n)}function ky(e,t){var n=0;switch(e.tag){case 13:var l=e.stateNode,u=e.memoizedState;u!==null&&(n=u.retryLane);break;case 19:l=e.stateNode;break;case 22:l=e.stateNode._retryCache;break;default:throw Error(r(314))}l!==null&&l.delete(t),_m(e,n)}function Xy(e,t){return bn(e,t)}var Ai=null,Pl=null,nc=!1,wi=!1,lc=!1,ml=0;function Zt(e){e!==Pl&&e.next===null&&(Pl===null?Ai=Pl=e:Pl=Pl.next=e),wi=!0,nc||(nc=!0,Qy())}function Ia(e,t){if(!lc&&wi){lc=!0;do for(var n=!1,l=Ai;l!==null;){if(e!==0){var u=l.pendingLanes;if(u===0)var i=0;else{var f=l.suspendedLanes,m=l.pingedLanes;i=(1<<31-mt(42|e)+1)-1,i&=u&~(f&~m),i=i&201326741?i&201326741|1:i?i|2:0}i!==0&&(n=!0,zm(l,i))}else i=Ae,i=Cu(l,l===Ue?i:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(i&3)===0||va(l,i)||(n=!0,zm(l,i));l=l.next}while(n);lc=!1}}function Vy(){Cm()}function Cm(){wi=nc=!1;var e=0;ml!==0&&(Iy()&&(e=ml),ml=0);for(var t=qt(),n=null,l=Ai;l!==null;){var u=l.next,i=Dm(l,t);i===0?(l.next=null,n===null?Ai=u:n.next=u,u===null&&(Pl=n)):(n=l,(e!==0||(i&3)!==0)&&(wi=!0)),l=u}Ia(e)}function Dm(e,t){for(var n=e.suspendedLanes,l=e.pingedLanes,u=e.expirationTimes,i=e.pendingLanes&-62914561;0<i;){var f=31-mt(i),m=1<<f,S=u[f];S===-1?((m&n)===0||(m&l)!==0)&&(u[f]=gp(m,t)):S<=t&&(e.expiredLanes|=m),i&=~m}if(t=Ue,n=Ae,n=Cu(e,e===t?n:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l=e.callbackNode,n===0||e===t&&(_e===2||_e===9)||e.cancelPendingCommit!==null)return l!==null&&l!==null&&vo(l),e.callbackNode=null,e.callbackPriority=0;if((n&3)===0||va(e,n)){if(t=n&-n,t===e.callbackPriority)return t;switch(l!==null&&vo(l),yo(n)){case 2:case 8:n=xs;break;case 32:n=Ru;break;case 268435456:n=Es;break;default:n=Ru}return l=Nm.bind(null,e),n=bn(n,l),e.callbackPriority=t,e.callbackNode=n,t}return l!==null&&l!==null&&vo(l),e.callbackPriority=2,e.callbackNode=null,2}function Nm(e,t){if($e!==0&&$e!==5)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(Ei()&&e.callbackNode!==n)return null;var l=Ae;return l=Cu(e,e===Ue?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l===0?null:(hm(e,l,t),Dm(e,qt()),e.callbackNode!=null&&e.callbackNode===n?Nm.bind(null,e):null)}function zm(e,t){if(Ei())return null;hm(e,t,!0)}function Qy(){t0(function(){(Oe&6)!==0?bn(Ss,Vy):Cm()})}function ac(){return ml===0&&(ml=As()),ml}function Um(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:ju(""+e)}function jm(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}function Zy(e,t,n,l,u){if(t==="submit"&&n&&n.stateNode===u){var i=Um((u[it]||null).action),f=l.submitter;f&&(t=(t=f[it]||null)?Um(t.formAction):f.getAttribute("formAction"),t!==null&&(i=t,f=null));var m=new Gu("action","action",null,l,u);e.push({event:m,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(ml!==0){var S=f?jm(u,f):new FormData(u);wr(n,{pending:!0,data:S,method:u.method,action:i},null,S)}}else typeof i=="function"&&(m.preventDefault(),S=f?jm(u,f):new FormData(u),wr(n,{pending:!0,data:S,method:u.method,action:i},i,S))},currentTarget:u}]})}}for(var uc=0;uc<Xo.length;uc++){var ic=Xo[uc],Ky=ic.toLowerCase(),Jy=ic[0].toUpperCase()+ic.slice(1);zt(Ky,"on"+Jy)}zt(hf,"onAnimationEnd"),zt(vf,"onAnimationIteration"),zt(gf,"onAnimationStart"),zt("dblclick","onDoubleClick"),zt("focusin","onFocus"),zt("focusout","onBlur"),zt(fy,"onTransitionRun"),zt(dy,"onTransitionStart"),zt(my,"onTransitionCancel"),zt(pf,"onTransitionEnd"),El("onMouseEnter",["mouseout","mouseover"]),El("onMouseLeave",["mouseout","mouseover"]),El("onPointerEnter",["pointerout","pointerover"]),El("onPointerLeave",["pointerout","pointerover"]),$n("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),$n("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),$n("onBeforeInput",["compositionend","keypress","textInput","paste"]),$n("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),$n("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),$n("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var eu="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Wy=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(eu));function Hm(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var l=e[n],u=l.event;l=l.listeners;e:{var i=void 0;if(t)for(var f=l.length-1;0<=f;f--){var m=l[f],S=m.instance,_=m.currentTarget;if(m=m.listener,S!==i&&u.isPropagationStopped())break e;i=m,u.currentTarget=_;try{i(u)}catch(B){fi(B)}u.currentTarget=null,i=S}else for(f=0;f<l.length;f++){if(m=l[f],S=m.instance,_=m.currentTarget,m=m.listener,S!==i&&u.isPropagationStopped())break e;i=m,u.currentTarget=_;try{i(u)}catch(B){fi(B)}u.currentTarget=null,i=S}}}}function Se(e,t){var n=t[bo];n===void 0&&(n=t[bo]=new Set);var l=e+"__bubble";n.has(l)||(Bm(t,e,2,!1),n.add(l))}function oc(e,t,n){var l=0;t&&(l|=4),Bm(n,e,l,t)}var Mi="_reactListening"+Math.random().toString(36).slice(2);function rc(e){if(!e[Mi]){e[Mi]=!0,_s.forEach(function(n){n!=="selectionchange"&&(Wy.has(n)||oc(n,!1,e),oc(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Mi]||(t[Mi]=!0,oc("selectionchange",!1,t))}}function Bm(e,t,n,l){switch(oh(t)){case 2:var u=E0;break;case 8:u=A0;break;default:u=Ec}n=u.bind(null,t,n,e),u=void 0,!Co||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(u=!0),l?u!==void 0?e.addEventListener(t,n,{capture:!0,passive:u}):e.addEventListener(t,n,!0):u!==void 0?e.addEventListener(t,n,{passive:u}):e.addEventListener(t,n,!1)}function cc(e,t,n,l,u){var i=l;if((t&1)===0&&(t&2)===0&&l!==null)e:for(;;){if(l===null)return;var f=l.tag;if(f===3||f===4){var m=l.stateNode.containerInfo;if(m===u)break;if(f===4)for(f=l.return;f!==null;){var S=f.tag;if((S===3||S===4)&&f.stateNode.containerInfo===u)return;f=f.return}for(;m!==null;){if(f=bl(m),f===null)return;if(S=f.tag,S===5||S===6||S===26||S===27){l=i=f;continue e}m=m.parentNode}}l=l.return}Xs(function(){var _=i,B=Oo(n),q=[];e:{var D=yf.get(e);if(D!==void 0){var N=Gu,ce=e;switch(e){case"keypress":if(Bu(n)===0)break e;case"keydown":case"keyup":N=Xp;break;case"focusin":ce="focus",N=Uo;break;case"focusout":ce="blur",N=Uo;break;case"beforeblur":case"afterblur":N=Uo;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":N=Zs;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":N=Dp;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":N=Zp;break;case hf:case vf:case gf:N=Up;break;case pf:N=Jp;break;case"scroll":case"scrollend":N=_p;break;case"wheel":N=Fp;break;case"copy":case"cut":case"paste":N=Hp;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":N=Js;break;case"toggle":case"beforetoggle":N=Pp}var ie=(t&4)!==0,Ne=!ie&&(e==="scroll"||e==="scrollend"),M=ie?D!==null?D+"Capture":null:D;ie=[];for(var A=_,R;A!==null;){var L=A;if(R=L.stateNode,L=L.tag,L!==5&&L!==26&&L!==27||R===null||M===null||(L=ba(A,M),L!=null&&ie.push(tu(A,L,R))),Ne)break;A=A.return}0<ie.length&&(D=new N(D,ce,null,n,B),q.push({event:D,listeners:ie}))}}if((t&7)===0){e:{if(D=e==="mouseover"||e==="pointerover",N=e==="mouseout"||e==="pointerout",D&&n!==Ro&&(ce=n.relatedTarget||n.fromElement)&&(bl(ce)||ce[yl]))break e;if((N||D)&&(D=B.window===B?B:(D=B.ownerDocument)?D.defaultView||D.parentWindow:window,N?(ce=n.relatedTarget||n.toElement,N=_,ce=ce?bl(ce):null,ce!==null&&(Ne=d(ce),ie=ce.tag,ce!==Ne||ie!==5&&ie!==27&&ie!==6)&&(ce=null)):(N=null,ce=_),N!==ce)){if(ie=Zs,L="onMouseLeave",M="onMouseEnter",A="mouse",(e==="pointerout"||e==="pointerover")&&(ie=Js,L="onPointerLeave",M="onPointerEnter",A="pointer"),Ne=N==null?D:ya(N),R=ce==null?D:ya(ce),D=new ie(L,A+"leave",N,n,B),D.target=Ne,D.relatedTarget=R,L=null,bl(B)===_&&(ie=new ie(M,A+"enter",ce,n,B),ie.target=R,ie.relatedTarget=Ne,L=ie),Ne=L,N&&ce)t:{for(ie=N,M=ce,A=0,R=ie;R;R=Il(R))A++;for(R=0,L=M;L;L=Il(L))R++;for(;0<A-R;)ie=Il(ie),A--;for(;0<R-A;)M=Il(M),R--;for(;A--;){if(ie===M||M!==null&&ie===M.alternate)break t;ie=Il(ie),M=Il(M)}ie=null}else ie=null;N!==null&&Lm(q,D,N,ie,!1),ce!==null&&Ne!==null&&Lm(q,Ne,ce,ie,!0)}}e:{if(D=_?ya(_):window,N=D.nodeName&&D.nodeName.toLowerCase(),N==="select"||N==="input"&&D.type==="file")var ee=nf;else if(ef(D))if(lf)ee=ry;else{ee=iy;var ye=uy}else N=D.nodeName,!N||N.toLowerCase()!=="input"||D.type!=="checkbox"&&D.type!=="radio"?_&&To(_.elementType)&&(ee=nf):ee=oy;if(ee&&(ee=ee(e,_))){tf(q,ee,n,B);break e}ye&&ye(e,D,_),e==="focusout"&&_&&D.type==="number"&&_.memoizedProps.value!=null&&Mo(D,"number",D.value)}switch(ye=_?ya(_):window,e){case"focusin":(ef(ye)||ye.contentEditable==="true")&&(_l=ye,Yo=_,Ra=null);break;case"focusout":Ra=Yo=_l=null;break;case"mousedown":qo=!0;break;case"contextmenu":case"mouseup":case"dragend":qo=!1,df(q,n,B);break;case"selectionchange":if(sy)break;case"keydown":case"keyup":df(q,n,B)}var le;if(Ho)e:{switch(e){case"compositionstart":var oe="onCompositionStart";break e;case"compositionend":oe="onCompositionEnd";break e;case"compositionupdate":oe="onCompositionUpdate";break e}oe=void 0}else Ol?Ps(e,n)&&(oe="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(oe="onCompositionStart");oe&&(Ws&&n.locale!=="ko"&&(Ol||oe!=="onCompositionStart"?oe==="onCompositionEnd"&&Ol&&(le=Vs()):(En=B,Do="value"in En?En.value:En.textContent,Ol=!0)),ye=Ti(_,oe),0<ye.length&&(oe=new Ks(oe,e,null,n,B),q.push({event:oe,listeners:ye}),le?oe.data=le:(le=Is(n),le!==null&&(oe.data=le)))),(le=ey?ty(e,n):ny(e,n))&&(oe=Ti(_,"onBeforeInput"),0<oe.length&&(ye=new Ks("onBeforeInput","beforeinput",null,n,B),q.push({event:ye,listeners:oe}),ye.data=le)),Zy(q,e,_,n,B)}Hm(q,t)})}function tu(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Ti(e,t){for(var n=t+"Capture",l=[];e!==null;){var u=e,i=u.stateNode;if(u=u.tag,u!==5&&u!==26&&u!==27||i===null||(u=ba(e,n),u!=null&&l.unshift(tu(e,u,i)),u=ba(e,t),u!=null&&l.push(tu(e,u,i))),e.tag===3)return l;e=e.return}return[]}function Il(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function Lm(e,t,n,l,u){for(var i=t._reactName,f=[];n!==null&&n!==l;){var m=n,S=m.alternate,_=m.stateNode;if(m=m.tag,S!==null&&S===l)break;m!==5&&m!==26&&m!==27||_===null||(S=_,u?(_=ba(n,i),_!=null&&f.unshift(tu(n,_,S))):u||(_=ba(n,i),_!=null&&f.push(tu(n,_,S)))),n=n.return}f.length!==0&&e.push({event:t,listeners:f})}var Fy=/\r\n?/g,$y=/\u0000|\uFFFD/g;function Gm(e){return(typeof e=="string"?e:""+e).replace(Fy,`
`).replace($y,"")}function Ym(e,t){return t=Gm(t),Gm(e)===t}function Ri(){}function De(e,t,n,l,u,i){switch(n){case"children":typeof l=="string"?t==="body"||t==="textarea"&&l===""||Ml(e,l):(typeof l=="number"||typeof l=="bigint")&&t!=="body"&&Ml(e,""+l);break;case"className":Nu(e,"class",l);break;case"tabIndex":Nu(e,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":Nu(e,n,l);break;case"style":qs(e,l,i);break;case"data":if(t!=="object"){Nu(e,"data",l);break}case"src":case"href":if(l===""&&(t!=="a"||n!=="href")){e.removeAttribute(n);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(n);break}l=ju(""+l),e.setAttribute(n,l);break;case"action":case"formAction":if(typeof l=="function"){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof i=="function"&&(n==="formAction"?(t!=="input"&&De(e,t,"name",u.name,u,null),De(e,t,"formEncType",u.formEncType,u,null),De(e,t,"formMethod",u.formMethod,u,null),De(e,t,"formTarget",u.formTarget,u,null)):(De(e,t,"encType",u.encType,u,null),De(e,t,"method",u.method,u,null),De(e,t,"target",u.target,u,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(n);break}l=ju(""+l),e.setAttribute(n,l);break;case"onClick":l!=null&&(e.onclick=Ri);break;case"onScroll":l!=null&&Se("scroll",e);break;case"onScrollEnd":l!=null&&Se("scrollend",e);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(r(61));if(n=l.__html,n!=null){if(u.children!=null)throw Error(r(60));e.innerHTML=n}}break;case"multiple":e.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":e.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){e.removeAttribute("xlink:href");break}n=ju(""+l),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(n,""+l):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":l===!0?e.setAttribute(n,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(n,l):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?e.setAttribute(n,l):e.removeAttribute(n);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?e.removeAttribute(n):e.setAttribute(n,l);break;case"popover":Se("beforetoggle",e),Se("toggle",e),Du(e,"popover",l);break;case"xlinkActuate":Pt(e,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":Pt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":Pt(e,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":Pt(e,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":Pt(e,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":Pt(e,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":Pt(e,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":Pt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":Pt(e,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":Du(e,"is",l);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=Rp.get(n)||n,Du(e,n,l))}}function sc(e,t,n,l,u,i){switch(n){case"style":qs(e,l,i);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(r(61));if(n=l.__html,n!=null){if(u.children!=null)throw Error(r(60));e.innerHTML=n}}break;case"children":typeof l=="string"?Ml(e,l):(typeof l=="number"||typeof l=="bigint")&&Ml(e,""+l);break;case"onScroll":l!=null&&Se("scroll",e);break;case"onScrollEnd":l!=null&&Se("scrollend",e);break;case"onClick":l!=null&&(e.onclick=Ri);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Cs.hasOwnProperty(n))e:{if(n[0]==="o"&&n[1]==="n"&&(u=n.endsWith("Capture"),t=n.slice(2,u?n.length-7:void 0),i=e[it]||null,i=i!=null?i[n]:null,typeof i=="function"&&e.removeEventListener(t,i,u),typeof l=="function")){typeof i!="function"&&i!==null&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,l,u);break e}n in e?e[n]=l:l===!0?e.setAttribute(n,""):Du(e,n,l)}}}function Pe(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Se("error",e),Se("load",e);var l=!1,u=!1,i;for(i in n)if(n.hasOwnProperty(i)){var f=n[i];if(f!=null)switch(i){case"src":l=!0;break;case"srcSet":u=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(r(137,t));default:De(e,t,i,f,n,null)}}u&&De(e,t,"srcSet",n.srcSet,n,null),l&&De(e,t,"src",n.src,n,null);return;case"input":Se("invalid",e);var m=i=f=u=null,S=null,_=null;for(l in n)if(n.hasOwnProperty(l)){var B=n[l];if(B!=null)switch(l){case"name":u=B;break;case"type":f=B;break;case"checked":S=B;break;case"defaultChecked":_=B;break;case"value":i=B;break;case"defaultValue":m=B;break;case"children":case"dangerouslySetInnerHTML":if(B!=null)throw Error(r(137,t));break;default:De(e,t,l,B,n,null)}}Bs(e,i,m,S,_,f,u,!1),zu(e);return;case"select":Se("invalid",e),l=f=i=null;for(u in n)if(n.hasOwnProperty(u)&&(m=n[u],m!=null))switch(u){case"value":i=m;break;case"defaultValue":f=m;break;case"multiple":l=m;default:De(e,t,u,m,n,null)}t=i,n=f,e.multiple=!!l,t!=null?wl(e,!!l,t,!1):n!=null&&wl(e,!!l,n,!0);return;case"textarea":Se("invalid",e),i=u=l=null;for(f in n)if(n.hasOwnProperty(f)&&(m=n[f],m!=null))switch(f){case"value":l=m;break;case"defaultValue":u=m;break;case"children":i=m;break;case"dangerouslySetInnerHTML":if(m!=null)throw Error(r(91));break;default:De(e,t,f,m,n,null)}Gs(e,l,u,i),zu(e);return;case"option":for(S in n)if(n.hasOwnProperty(S)&&(l=n[S],l!=null))switch(S){case"selected":e.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:De(e,t,S,l,n,null)}return;case"dialog":Se("beforetoggle",e),Se("toggle",e),Se("cancel",e),Se("close",e);break;case"iframe":case"object":Se("load",e);break;case"video":case"audio":for(l=0;l<eu.length;l++)Se(eu[l],e);break;case"image":Se("error",e),Se("load",e);break;case"details":Se("toggle",e);break;case"embed":case"source":case"link":Se("error",e),Se("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(_ in n)if(n.hasOwnProperty(_)&&(l=n[_],l!=null))switch(_){case"children":case"dangerouslySetInnerHTML":throw Error(r(137,t));default:De(e,t,_,l,n,null)}return;default:if(To(t)){for(B in n)n.hasOwnProperty(B)&&(l=n[B],l!==void 0&&sc(e,t,B,l,n,void 0));return}}for(m in n)n.hasOwnProperty(m)&&(l=n[m],l!=null&&De(e,t,m,l,n,null))}function Py(e,t,n,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var u=null,i=null,f=null,m=null,S=null,_=null,B=null;for(N in n){var q=n[N];if(n.hasOwnProperty(N)&&q!=null)switch(N){case"checked":break;case"value":break;case"defaultValue":S=q;default:l.hasOwnProperty(N)||De(e,t,N,null,l,q)}}for(var D in l){var N=l[D];if(q=n[D],l.hasOwnProperty(D)&&(N!=null||q!=null))switch(D){case"type":i=N;break;case"name":u=N;break;case"checked":_=N;break;case"defaultChecked":B=N;break;case"value":f=N;break;case"defaultValue":m=N;break;case"children":case"dangerouslySetInnerHTML":if(N!=null)throw Error(r(137,t));break;default:N!==q&&De(e,t,D,N,l,q)}}wo(e,f,m,S,_,B,i,u);return;case"select":N=f=m=D=null;for(i in n)if(S=n[i],n.hasOwnProperty(i)&&S!=null)switch(i){case"value":break;case"multiple":N=S;default:l.hasOwnProperty(i)||De(e,t,i,null,l,S)}for(u in l)if(i=l[u],S=n[u],l.hasOwnProperty(u)&&(i!=null||S!=null))switch(u){case"value":D=i;break;case"defaultValue":m=i;break;case"multiple":f=i;default:i!==S&&De(e,t,u,i,l,S)}t=m,n=f,l=N,D!=null?wl(e,!!n,D,!1):!!l!=!!n&&(t!=null?wl(e,!!n,t,!0):wl(e,!!n,n?[]:"",!1));return;case"textarea":N=D=null;for(m in n)if(u=n[m],n.hasOwnProperty(m)&&u!=null&&!l.hasOwnProperty(m))switch(m){case"value":break;case"children":break;default:De(e,t,m,null,l,u)}for(f in l)if(u=l[f],i=n[f],l.hasOwnProperty(f)&&(u!=null||i!=null))switch(f){case"value":D=u;break;case"defaultValue":N=u;break;case"children":break;case"dangerouslySetInnerHTML":if(u!=null)throw Error(r(91));break;default:u!==i&&De(e,t,f,u,l,i)}Ls(e,D,N);return;case"option":for(var ce in n)if(D=n[ce],n.hasOwnProperty(ce)&&D!=null&&!l.hasOwnProperty(ce))switch(ce){case"selected":e.selected=!1;break;default:De(e,t,ce,null,l,D)}for(S in l)if(D=l[S],N=n[S],l.hasOwnProperty(S)&&D!==N&&(D!=null||N!=null))switch(S){case"selected":e.selected=D&&typeof D!="function"&&typeof D!="symbol";break;default:De(e,t,S,D,l,N)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var ie in n)D=n[ie],n.hasOwnProperty(ie)&&D!=null&&!l.hasOwnProperty(ie)&&De(e,t,ie,null,l,D);for(_ in l)if(D=l[_],N=n[_],l.hasOwnProperty(_)&&D!==N&&(D!=null||N!=null))switch(_){case"children":case"dangerouslySetInnerHTML":if(D!=null)throw Error(r(137,t));break;default:De(e,t,_,D,l,N)}return;default:if(To(t)){for(var Ne in n)D=n[Ne],n.hasOwnProperty(Ne)&&D!==void 0&&!l.hasOwnProperty(Ne)&&sc(e,t,Ne,void 0,l,D);for(B in l)D=l[B],N=n[B],!l.hasOwnProperty(B)||D===N||D===void 0&&N===void 0||sc(e,t,B,D,l,N);return}}for(var M in n)D=n[M],n.hasOwnProperty(M)&&D!=null&&!l.hasOwnProperty(M)&&De(e,t,M,null,l,D);for(q in l)D=l[q],N=n[q],!l.hasOwnProperty(q)||D===N||D==null&&N==null||De(e,t,q,D,l,N)}var fc=null,dc=null;function Oi(e){return e.nodeType===9?e:e.ownerDocument}function qm(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function km(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function mc(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var hc=null;function Iy(){var e=window.event;return e&&e.type==="popstate"?e===hc?!1:(hc=e,!0):(hc=null,!1)}var Xm=typeof setTimeout=="function"?setTimeout:void 0,e0=typeof clearTimeout=="function"?clearTimeout:void 0,Vm=typeof Promise=="function"?Promise:void 0,t0=typeof queueMicrotask=="function"?queueMicrotask:typeof Vm<"u"?function(e){return Vm.resolve(null).then(e).catch(n0)}:Xm;function n0(e){setTimeout(function(){throw e})}function Ln(e){return e==="head"}function Qm(e,t){var n=t,l=0,u=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(0<l&&8>l){n=l;var f=e.ownerDocument;if(n&1&&nu(f.documentElement),n&2&&nu(f.body),n&4)for(n=f.head,nu(n),f=n.firstChild;f;){var m=f.nextSibling,S=f.nodeName;f[pa]||S==="SCRIPT"||S==="STYLE"||S==="LINK"&&f.rel.toLowerCase()==="stylesheet"||n.removeChild(f),f=m}}if(u===0){e.removeChild(i),su(t);return}u--}else n==="$"||n==="$?"||n==="$!"?u++:l=n.charCodeAt(0)-48;else l=0;n=i}while(n);su(t)}function vc(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":vc(n),So(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}e.removeChild(n)}}function l0(e,t,n,l){for(;e.nodeType===1;){var u=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!l&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(l){if(!e[pa])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(i=e.getAttribute("rel"),i==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(i!==u.rel||e.getAttribute("href")!==(u.href==null||u.href===""?null:u.href)||e.getAttribute("crossorigin")!==(u.crossOrigin==null?null:u.crossOrigin)||e.getAttribute("title")!==(u.title==null?null:u.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(i=e.getAttribute("src"),(i!==(u.src==null?null:u.src)||e.getAttribute("type")!==(u.type==null?null:u.type)||e.getAttribute("crossorigin")!==(u.crossOrigin==null?null:u.crossOrigin))&&i&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var i=u.name==null?null:""+u.name;if(u.type==="hidden"&&e.getAttribute("name")===i)return e}else return e;if(e=jt(e.nextSibling),e===null)break}return null}function a0(e,t,n){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!n||(e=jt(e.nextSibling),e===null))return null;return e}function gc(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function u0(e,t){var n=e.ownerDocument;if(e.data!=="$?"||n.readyState==="complete")t();else{var l=function(){t(),n.removeEventListener("DOMContentLoaded",l)};n.addEventListener("DOMContentLoaded",l),e._reactRetry=l}}function jt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var pc=null;function Zm(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}function Km(e,t,n){switch(t=Oi(n),e){case"html":if(e=t.documentElement,!e)throw Error(r(452));return e;case"head":if(e=t.head,!e)throw Error(r(453));return e;case"body":if(e=t.body,!e)throw Error(r(454));return e;default:throw Error(r(451))}}function nu(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);So(e)}var Dt=new Map,Jm=new Set;function _i(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var mn=Q.d;Q.d={f:i0,r:o0,D:r0,C:c0,L:s0,m:f0,X:m0,S:d0,M:h0};function i0(){var e=mn.f(),t=Si();return e||t}function o0(e){var t=Sl(e);t!==null&&t.tag===5&&t.type==="form"?hd(t):mn.r(e)}var ea=typeof document>"u"?null:document;function Wm(e,t,n){var l=ea;if(l&&typeof t=="string"&&t){var u=wt(t);u='link[rel="'+e+'"][href="'+u+'"]',typeof n=="string"&&(u+='[crossorigin="'+n+'"]'),Jm.has(u)||(Jm.add(u),e={rel:e,crossOrigin:n,href:t},l.querySelector(u)===null&&(t=l.createElement("link"),Pe(t,"link",e),Ze(t),l.head.appendChild(t)))}}function r0(e){mn.D(e),Wm("dns-prefetch",e,null)}function c0(e,t){mn.C(e,t),Wm("preconnect",e,t)}function s0(e,t,n){mn.L(e,t,n);var l=ea;if(l&&e&&t){var u='link[rel="preload"][as="'+wt(t)+'"]';t==="image"&&n&&n.imageSrcSet?(u+='[imagesrcset="'+wt(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(u+='[imagesizes="'+wt(n.imageSizes)+'"]')):u+='[href="'+wt(e)+'"]';var i=u;switch(t){case"style":i=ta(e);break;case"script":i=na(e)}Dt.has(i)||(e=p({rel:"preload",href:t==="image"&&n&&n.imageSrcSet?void 0:e,as:t},n),Dt.set(i,e),l.querySelector(u)!==null||t==="style"&&l.querySelector(lu(i))||t==="script"&&l.querySelector(au(i))||(t=l.createElement("link"),Pe(t,"link",e),Ze(t),l.head.appendChild(t)))}}function f0(e,t){mn.m(e,t);var n=ea;if(n&&e){var l=t&&typeof t.as=="string"?t.as:"script",u='link[rel="modulepreload"][as="'+wt(l)+'"][href="'+wt(e)+'"]',i=u;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":i=na(e)}if(!Dt.has(i)&&(e=p({rel:"modulepreload",href:e},t),Dt.set(i,e),n.querySelector(u)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(au(i)))return}l=n.createElement("link"),Pe(l,"link",e),Ze(l),n.head.appendChild(l)}}}function d0(e,t,n){mn.S(e,t,n);var l=ea;if(l&&e){var u=xl(l).hoistableStyles,i=ta(e);t=t||"default";var f=u.get(i);if(!f){var m={loading:0,preload:null};if(f=l.querySelector(lu(i)))m.loading=5;else{e=p({rel:"stylesheet",href:e,"data-precedence":t},n),(n=Dt.get(i))&&yc(e,n);var S=f=l.createElement("link");Ze(S),Pe(S,"link",e),S._p=new Promise(function(_,B){S.onload=_,S.onerror=B}),S.addEventListener("load",function(){m.loading|=1}),S.addEventListener("error",function(){m.loading|=2}),m.loading|=4,Ci(f,t,l)}f={type:"stylesheet",instance:f,count:1,state:m},u.set(i,f)}}}function m0(e,t){mn.X(e,t);var n=ea;if(n&&e){var l=xl(n).hoistableScripts,u=na(e),i=l.get(u);i||(i=n.querySelector(au(u)),i||(e=p({src:e,async:!0},t),(t=Dt.get(u))&&bc(e,t),i=n.createElement("script"),Ze(i),Pe(i,"link",e),n.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},l.set(u,i))}}function h0(e,t){mn.M(e,t);var n=ea;if(n&&e){var l=xl(n).hoistableScripts,u=na(e),i=l.get(u);i||(i=n.querySelector(au(u)),i||(e=p({src:e,async:!0,type:"module"},t),(t=Dt.get(u))&&bc(e,t),i=n.createElement("script"),Ze(i),Pe(i,"link",e),n.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},l.set(u,i))}}function Fm(e,t,n,l){var u=(u=ae.current)?_i(u):null;if(!u)throw Error(r(446));switch(e){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(t=ta(n.href),n=xl(u).hoistableStyles,l=n.get(t),l||(l={type:"style",instance:null,count:0,state:null},n.set(t,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){e=ta(n.href);var i=xl(u).hoistableStyles,f=i.get(e);if(f||(u=u.ownerDocument||u,f={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},i.set(e,f),(i=u.querySelector(lu(e)))&&!i._p&&(f.instance=i,f.state.loading=5),Dt.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Dt.set(e,n),i||v0(u,e,n,f.state))),t&&l===null)throw Error(r(528,""));return f}if(t&&l!==null)throw Error(r(529,""));return null;case"script":return t=n.async,n=n.src,typeof n=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=na(n),n=xl(u).hoistableScripts,l=n.get(t),l||(l={type:"script",instance:null,count:0,state:null},n.set(t,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(r(444,e))}}function ta(e){return'href="'+wt(e)+'"'}function lu(e){return'link[rel="stylesheet"]['+e+"]"}function $m(e){return p({},e,{"data-precedence":e.precedence,precedence:null})}function v0(e,t,n,l){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?l.loading=1:(t=e.createElement("link"),l.preload=t,t.addEventListener("load",function(){return l.loading|=1}),t.addEventListener("error",function(){return l.loading|=2}),Pe(t,"link",n),Ze(t),e.head.appendChild(t))}function na(e){return'[src="'+wt(e)+'"]'}function au(e){return"script[async]"+e}function Pm(e,t,n){if(t.count++,t.instance===null)switch(t.type){case"style":var l=e.querySelector('style[data-href~="'+wt(n.href)+'"]');if(l)return t.instance=l,Ze(l),l;var u=p({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return l=(e.ownerDocument||e).createElement("style"),Ze(l),Pe(l,"style",u),Ci(l,n.precedence,e),t.instance=l;case"stylesheet":u=ta(n.href);var i=e.querySelector(lu(u));if(i)return t.state.loading|=4,t.instance=i,Ze(i),i;l=$m(n),(u=Dt.get(u))&&yc(l,u),i=(e.ownerDocument||e).createElement("link"),Ze(i);var f=i;return f._p=new Promise(function(m,S){f.onload=m,f.onerror=S}),Pe(i,"link",l),t.state.loading|=4,Ci(i,n.precedence,e),t.instance=i;case"script":return i=na(n.src),(u=e.querySelector(au(i)))?(t.instance=u,Ze(u),u):(l=n,(u=Dt.get(i))&&(l=p({},n),bc(l,u)),e=e.ownerDocument||e,u=e.createElement("script"),Ze(u),Pe(u,"link",l),e.head.appendChild(u),t.instance=u);case"void":return null;default:throw Error(r(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(l=t.instance,t.state.loading|=4,Ci(l,n.precedence,e));return t.instance}function Ci(e,t,n){for(var l=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),u=l.length?l[l.length-1]:null,i=u,f=0;f<l.length;f++){var m=l[f];if(m.dataset.precedence===t)i=m;else if(i!==u)break}i?i.parentNode.insertBefore(e,i.nextSibling):(t=n.nodeType===9?n.head:n,t.insertBefore(e,t.firstChild))}function yc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function bc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var Di=null;function Im(e,t,n){if(Di===null){var l=new Map,u=Di=new Map;u.set(n,l)}else u=Di,l=u.get(n),l||(l=new Map,u.set(n,l));if(l.has(e))return l;for(l.set(e,null),n=n.getElementsByTagName(e),u=0;u<n.length;u++){var i=n[u];if(!(i[pa]||i[Ie]||e==="link"&&i.getAttribute("rel")==="stylesheet")&&i.namespaceURI!=="http://www.w3.org/2000/svg"){var f=i.getAttribute(t)||"";f=e+f;var m=l.get(f);m?m.push(i):l.set(f,[i])}}return l}function eh(e,t,n){e=e.ownerDocument||e,e.head.insertBefore(n,t==="title"?e.querySelector("head > title"):null)}function g0(e,t,n){if(n===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function th(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var uu=null;function p0(){}function y0(e,t,n){if(uu===null)throw Error(r(475));var l=uu;if(t.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var u=ta(n.href),i=e.querySelector(lu(u));if(i){e=i._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(l.count++,l=Ni.bind(l),e.then(l,l)),t.state.loading|=4,t.instance=i,Ze(i);return}i=e.ownerDocument||e,n=$m(n),(u=Dt.get(u))&&yc(n,u),i=i.createElement("link"),Ze(i);var f=i;f._p=new Promise(function(m,S){f.onload=m,f.onerror=S}),Pe(i,"link",n),t.instance=i}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(l.count++,t=Ni.bind(l),e.addEventListener("load",t),e.addEventListener("error",t))}}function b0(){if(uu===null)throw Error(r(475));var e=uu;return e.stylesheets&&e.count===0&&Sc(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&Sc(e,e.stylesheets),e.unsuspend){var l=e.unsuspend;e.unsuspend=null,l()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}function Ni(){if(this.count--,this.count===0){if(this.stylesheets)Sc(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var zi=null;function Sc(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,zi=new Map,t.forEach(S0,e),zi=null,Ni.call(e))}function S0(e,t){if(!(t.state.loading&4)){var n=zi.get(e);if(n)var l=n.get(null);else{n=new Map,zi.set(e,n);for(var u=e.querySelectorAll("link[data-precedence],style[data-precedence]"),i=0;i<u.length;i++){var f=u[i];(f.nodeName==="LINK"||f.getAttribute("media")!=="not all")&&(n.set(f.dataset.precedence,f),l=f)}l&&n.set(null,l)}u=t.instance,f=u.getAttribute("data-precedence"),i=n.get(f)||l,i===l&&n.set(null,u),n.set(f,u),this.count++,l=Ni.bind(this),u.addEventListener("load",l),u.addEventListener("error",l),i?i.parentNode.insertBefore(u,i.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(u,e.firstChild)),t.state.loading|=4}}var iu={$$typeof:k,Provider:null,Consumer:null,_currentValue:j,_currentValue2:j,_threadCount:0};function x0(e,t,n,l,u,i,f,m){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=go(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=go(0),this.hiddenUpdates=go(null),this.identifierPrefix=l,this.onUncaughtError=u,this.onCaughtError=i,this.onRecoverableError=f,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=m,this.incompleteTransitions=new Map}function nh(e,t,n,l,u,i,f,m,S,_,B,q){return e=new x0(e,t,n,f,m,S,_,q),t=1,i===!0&&(t|=24),i=vt(3,null,null,t),e.current=i,i.stateNode=e,t=tr(),t.refCount++,e.pooledCache=t,t.refCount++,i.memoizedState={element:l,isDehydrated:n,cache:t},ur(i),e}function lh(e){return e?(e=zl,e):zl}function ah(e,t,n,l,u,i){u=lh(u),l.context===null?l.context=u:l.pendingContext=u,l=Mn(t),l.payload={element:n},i=i===void 0?null:i,i!==null&&(l.callback=i),n=Tn(e,l,t),n!==null&&(St(n,e,t),Ha(n,e,t))}function uh(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function xc(e,t){uh(e,t),(e=e.alternate)&&uh(e,t)}function ih(e){if(e.tag===13){var t=Nl(e,67108864);t!==null&&St(t,e,67108864),xc(e,67108864)}}var Ui=!0;function E0(e,t,n,l){var u=O.T;O.T=null;var i=Q.p;try{Q.p=2,Ec(e,t,n,l)}finally{Q.p=i,O.T=u}}function A0(e,t,n,l){var u=O.T;O.T=null;var i=Q.p;try{Q.p=8,Ec(e,t,n,l)}finally{Q.p=i,O.T=u}}function Ec(e,t,n,l){if(Ui){var u=Ac(l);if(u===null)cc(e,t,l,ji,n),rh(e,l);else if(M0(u,e,t,n,l))l.stopPropagation();else if(rh(e,l),t&4&&-1<w0.indexOf(e)){for(;u!==null;){var i=Sl(u);if(i!==null)switch(i.tag){case 3:if(i=i.stateNode,i.current.memoizedState.isDehydrated){var f=Fn(i.pendingLanes);if(f!==0){var m=i;for(m.pendingLanes|=2,m.entangledLanes|=2;f;){var S=1<<31-mt(f);m.entanglements[1]|=S,f&=~S}Zt(i),(Oe&6)===0&&(yi=qt()+500,Ia(0))}}break;case 13:m=Nl(i,2),m!==null&&St(m,i,2),Si(),xc(i,2)}if(i=Ac(l),i===null&&cc(e,t,l,ji,n),i===u)break;u=i}u!==null&&l.stopPropagation()}else cc(e,t,l,null,n)}}function Ac(e){return e=Oo(e),wc(e)}var ji=null;function wc(e){if(ji=null,e=bl(e),e!==null){var t=d(e);if(t===null)e=null;else{var n=t.tag;if(n===13){if(e=v(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return ji=e,null}function oh(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(cp()){case Ss:return 2;case xs:return 8;case Ru:case sp:return 32;case Es:return 268435456;default:return 32}default:return 32}}var Mc=!1,Gn=null,Yn=null,qn=null,ou=new Map,ru=new Map,kn=[],w0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function rh(e,t){switch(e){case"focusin":case"focusout":Gn=null;break;case"dragenter":case"dragleave":Yn=null;break;case"mouseover":case"mouseout":qn=null;break;case"pointerover":case"pointerout":ou.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":ru.delete(t.pointerId)}}function cu(e,t,n,l,u,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:l,nativeEvent:i,targetContainers:[u]},t!==null&&(t=Sl(t),t!==null&&ih(t)),e):(e.eventSystemFlags|=l,t=e.targetContainers,u!==null&&t.indexOf(u)===-1&&t.push(u),e)}function M0(e,t,n,l,u){switch(t){case"focusin":return Gn=cu(Gn,e,t,n,l,u),!0;case"dragenter":return Yn=cu(Yn,e,t,n,l,u),!0;case"mouseover":return qn=cu(qn,e,t,n,l,u),!0;case"pointerover":var i=u.pointerId;return ou.set(i,cu(ou.get(i)||null,e,t,n,l,u)),!0;case"gotpointercapture":return i=u.pointerId,ru.set(i,cu(ru.get(i)||null,e,t,n,l,u)),!0}return!1}function ch(e){var t=bl(e.target);if(t!==null){var n=d(t);if(n!==null){if(t=n.tag,t===13){if(t=v(n),t!==null){e.blockedOn=t,yp(e.priority,function(){if(n.tag===13){var l=bt();l=po(l);var u=Nl(n,l);u!==null&&St(u,n,l),xc(n,l)}});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Hi(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Ac(e.nativeEvent);if(n===null){n=e.nativeEvent;var l=new n.constructor(n.type,n);Ro=l,n.target.dispatchEvent(l),Ro=null}else return t=Sl(n),t!==null&&ih(t),e.blockedOn=n,!1;t.shift()}return!0}function sh(e,t,n){Hi(e)&&n.delete(t)}function T0(){Mc=!1,Gn!==null&&Hi(Gn)&&(Gn=null),Yn!==null&&Hi(Yn)&&(Yn=null),qn!==null&&Hi(qn)&&(qn=null),ou.forEach(sh),ru.forEach(sh)}function Bi(e,t){e.blockedOn===t&&(e.blockedOn=null,Mc||(Mc=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,T0)))}var Li=null;function fh(e){Li!==e&&(Li=e,a.unstable_scheduleCallback(a.unstable_NormalPriority,function(){Li===e&&(Li=null);for(var t=0;t<e.length;t+=3){var n=e[t],l=e[t+1],u=e[t+2];if(typeof l!="function"){if(wc(l||n)===null)continue;break}var i=Sl(n);i!==null&&(e.splice(t,3),t-=3,wr(i,{pending:!0,data:u,method:n.method,action:l},l,u))}}))}function su(e){function t(S){return Bi(S,e)}Gn!==null&&Bi(Gn,e),Yn!==null&&Bi(Yn,e),qn!==null&&Bi(qn,e),ou.forEach(t),ru.forEach(t);for(var n=0;n<kn.length;n++){var l=kn[n];l.blockedOn===e&&(l.blockedOn=null)}for(;0<kn.length&&(n=kn[0],n.blockedOn===null);)ch(n),n.blockedOn===null&&kn.shift();if(n=(e.ownerDocument||e).$$reactFormReplay,n!=null)for(l=0;l<n.length;l+=3){var u=n[l],i=n[l+1],f=u[it]||null;if(typeof i=="function")f||fh(n);else if(f){var m=null;if(i&&i.hasAttribute("formAction")){if(u=i,f=i[it]||null)m=f.formAction;else if(wc(u)!==null)continue}else m=f.action;typeof m=="function"?n[l+1]=m:(n.splice(l,3),l-=3),fh(n)}}}function Tc(e){this._internalRoot=e}Gi.prototype.render=Tc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(r(409));var n=t.current,l=bt();ah(n,l,e,t,null,null)},Gi.prototype.unmount=Tc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;ah(e.current,2,null,e,null,null),Si(),t[yl]=null}};function Gi(e){this._internalRoot=e}Gi.prototype.unstable_scheduleHydration=function(e){if(e){var t=Rs();e={blockedOn:null,target:e,priority:t};for(var n=0;n<kn.length&&t!==0&&t<kn[n].priority;n++);kn.splice(n,0,e),n===0&&ch(e)}};var dh=o.version;if(dh!=="19.1.1")throw Error(r(527,dh,"19.1.1"));Q.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(r(188)):(e=Object.keys(e).join(","),Error(r(268,e)));return e=y(t),e=e!==null?h(e):null,e=e===null?null:e.stateNode,e};var R0={bundleType:0,version:"19.1.1",rendererPackageName:"react-dom",currentDispatcherRef:O,reconcilerVersion:"19.1.1"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Yi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Yi.isDisabled&&Yi.supportsFiber)try{ha=Yi.inject(R0),dt=Yi}catch{}}return du.createRoot=function(e,t){if(!s(e))throw Error(r(299));var n=!1,l="",u=Od,i=_d,f=Cd,m=null;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(l=t.identifierPrefix),t.onUncaughtError!==void 0&&(u=t.onUncaughtError),t.onCaughtError!==void 0&&(i=t.onCaughtError),t.onRecoverableError!==void 0&&(f=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(m=t.unstable_transitionCallbacks)),t=nh(e,1,!1,null,null,n,l,u,i,f,m,null),e[yl]=t.current,rc(e),new Tc(t)},du.hydrateRoot=function(e,t,n){if(!s(e))throw Error(r(299));var l=!1,u="",i=Od,f=_d,m=Cd,S=null,_=null;return n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(u=n.identifierPrefix),n.onUncaughtError!==void 0&&(i=n.onUncaughtError),n.onCaughtError!==void 0&&(f=n.onCaughtError),n.onRecoverableError!==void 0&&(m=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(S=n.unstable_transitionCallbacks),n.formState!==void 0&&(_=n.formState)),t=nh(e,1,!0,t,n??null,l,u,i,f,m,S,_),t.context=lh(null),n=t.current,l=bt(),l=po(l),u=Mn(l),u.callback=null,Tn(n,u,l),n=l,t.current.lanes=n,ga(t,n),Zt(t),e[yl]=t.current,rc(e),new Gi(t)},du.version="19.1.1",du}var Eh;function B0(){if(Eh)return _c.exports;Eh=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(o){console.error(o)}}return a(),_c.exports=H0(),_c.exports}var L0=B0();function Ah(a,o){if(typeof a=="function")return a(o);a!=null&&(a.current=o)}function uo(...a){return o=>{let c=!1;const r=a.map(s=>{const d=Ah(s,o);return!c&&typeof d=="function"&&(c=!0),d});if(c)return()=>{for(let s=0;s<r.length;s++){const d=r[s];typeof d=="function"?d():Ah(a[s],null)}}}}function ft(...a){return b.useCallback(uo(...a),a)}function vu(a){const o=Y0(a),c=b.forwardRef((r,s)=>{const{children:d,...v}=r,g=b.Children.toArray(d),y=g.find(k0);if(y){const h=y.props.children,p=g.map(x=>x===y?b.Children.count(h)>1?b.Children.only(null):b.isValidElement(h)?h.props.children:null:x);return H.jsx(o,{...v,ref:s,children:b.isValidElement(h)?b.cloneElement(h,void 0,p):null})}return H.jsx(o,{...v,ref:s,children:d})});return c.displayName=`${a}.Slot`,c}var G0=vu("Slot");function Y0(a){const o=b.forwardRef((c,r)=>{const{children:s,...d}=c;if(b.isValidElement(s)){const v=V0(s),g=X0(d,s.props);return s.type!==b.Fragment&&(g.ref=r?uo(r,v):v),b.cloneElement(s,g)}return b.Children.count(s)>1?b.Children.only(null):null});return o.displayName=`${a}.SlotClone`,o}var q0=Symbol("radix.slottable");function k0(a){return b.isValidElement(a)&&typeof a.type=="function"&&"__radixId"in a.type&&a.type.__radixId===q0}function X0(a,o){const c={...o};for(const r in o){const s=a[r],d=o[r];/^on[A-Z]/.test(r)?s&&d?c[r]=(...g)=>{const y=d(...g);return s(...g),y}:s&&(c[r]=s):r==="style"?c[r]={...s,...d}:r==="className"&&(c[r]=[s,d].filter(Boolean).join(" "))}return{...a,...c}}function V0(a){let o=Object.getOwnPropertyDescriptor(a.props,"ref")?.get,c=o&&"isReactWarning"in o&&o.isReactWarning;return c?a.ref:(o=Object.getOwnPropertyDescriptor(a,"ref")?.get,c=o&&"isReactWarning"in o&&o.isReactWarning,c?a.props.ref:a.props.ref||a.ref)}function av(a){var o,c,r="";if(typeof a=="string"||typeof a=="number")r+=a;else if(typeof a=="object")if(Array.isArray(a)){var s=a.length;for(o=0;o<s;o++)a[o]&&(c=av(a[o]))&&(r&&(r+=" "),r+=c)}else for(c in a)a[c]&&(r&&(r+=" "),r+=c);return r}function uv(){for(var a,o,c=0,r="",s=arguments.length;c<s;c++)(a=arguments[c])&&(o=av(a))&&(r&&(r+=" "),r+=o);return r}const wh=a=>typeof a=="boolean"?`${a}`:a===0?"0":a,Mh=uv,Q0=(a,o)=>c=>{var r;if(o?.variants==null)return Mh(a,c?.class,c?.className);const{variants:s,defaultVariants:d}=o,v=Object.keys(s).map(h=>{const p=c?.[h],x=d?.[h];if(p===null)return null;const w=wh(p)||wh(x);return s[h][w]}),g=c&&Object.entries(c).reduce((h,p)=>{let[x,w]=p;return w===void 0||(h[x]=w),h},{}),y=o==null||(r=o.compoundVariants)===null||r===void 0?void 0:r.reduce((h,p)=>{let{class:x,className:w,...C}=p;return Object.entries(C).every(z=>{let[T,U]=z;return Array.isArray(U)?U.includes({...d,...g}[T]):{...d,...g}[T]===U})?[...h,x,w]:h},[]);return Mh(a,v,y,c?.class,c?.className)},ls="-",Z0=a=>{const o=J0(a),{conflictingClassGroups:c,conflictingClassGroupModifiers:r}=a;return{getClassGroupId:v=>{const g=v.split(ls);return g[0]===""&&g.length!==1&&g.shift(),iv(g,o)||K0(v)},getConflictingClassGroupIds:(v,g)=>{const y=c[v]||[];return g&&r[v]?[...y,...r[v]]:y}}},iv=(a,o)=>{if(a.length===0)return o.classGroupId;const c=a[0],r=o.nextPart.get(c),s=r?iv(a.slice(1),r):void 0;if(s)return s;if(o.validators.length===0)return;const d=a.join(ls);return o.validators.find(({validator:v})=>v(d))?.classGroupId},Th=/^\[(.+)\]$/,K0=a=>{if(Th.test(a)){const o=Th.exec(a)[1],c=o?.substring(0,o.indexOf(":"));if(c)return"arbitrary.."+c}},J0=a=>{const{theme:o,classGroups:c}=a,r={nextPart:new Map,validators:[]};for(const s in c)Qc(c[s],r,s,o);return r},Qc=(a,o,c,r)=>{a.forEach(s=>{if(typeof s=="string"){const d=s===""?o:Rh(o,s);d.classGroupId=c;return}if(typeof s=="function"){if(W0(s)){Qc(s(r),o,c,r);return}o.validators.push({validator:s,classGroupId:c});return}Object.entries(s).forEach(([d,v])=>{Qc(v,Rh(o,d),c,r)})})},Rh=(a,o)=>{let c=a;return o.split(ls).forEach(r=>{c.nextPart.has(r)||c.nextPart.set(r,{nextPart:new Map,validators:[]}),c=c.nextPart.get(r)}),c},W0=a=>a.isThemeGetter,F0=a=>{if(a<1)return{get:()=>{},set:()=>{}};let o=0,c=new Map,r=new Map;const s=(d,v)=>{c.set(d,v),o++,o>a&&(o=0,r=c,c=new Map)};return{get(d){let v=c.get(d);if(v!==void 0)return v;if((v=r.get(d))!==void 0)return s(d,v),v},set(d,v){c.has(d)?c.set(d,v):s(d,v)}}},Zc="!",Kc=":",$0=Kc.length,P0=a=>{const{prefix:o,experimentalParseClassName:c}=a;let r=s=>{const d=[];let v=0,g=0,y=0,h;for(let z=0;z<s.length;z++){let T=s[z];if(v===0&&g===0){if(T===Kc){d.push(s.slice(y,z)),y=z+$0;continue}if(T==="/"){h=z;continue}}T==="["?v++:T==="]"?v--:T==="("?g++:T===")"&&g--}const p=d.length===0?s:s.substring(y),x=I0(p),w=x!==p,C=h&&h>y?h-y:void 0;return{modifiers:d,hasImportantModifier:w,baseClassName:x,maybePostfixModifierPosition:C}};if(o){const s=o+Kc,d=r;r=v=>v.startsWith(s)?d(v.substring(s.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:v,maybePostfixModifierPosition:void 0}}if(c){const s=r;r=d=>c({className:d,parseClassName:s})}return r},I0=a=>a.endsWith(Zc)?a.substring(0,a.length-1):a.startsWith(Zc)?a.substring(1):a,eb=a=>{const o=Object.fromEntries(a.orderSensitiveModifiers.map(r=>[r,!0]));return r=>{if(r.length<=1)return r;const s=[];let d=[];return r.forEach(v=>{v[0]==="["||o[v]?(s.push(...d.sort(),v),d=[]):d.push(v)}),s.push(...d.sort()),s}},tb=a=>({cache:F0(a.cacheSize),parseClassName:P0(a),sortModifiers:eb(a),...Z0(a)}),nb=/\s+/,lb=(a,o)=>{const{parseClassName:c,getClassGroupId:r,getConflictingClassGroupIds:s,sortModifiers:d}=o,v=[],g=a.trim().split(nb);let y="";for(let h=g.length-1;h>=0;h-=1){const p=g[h],{isExternal:x,modifiers:w,hasImportantModifier:C,baseClassName:z,maybePostfixModifierPosition:T}=c(p);if(x){y=p+(y.length>0?" "+y:y);continue}let U=!!T,Y=r(U?z.substring(0,T):z);if(!Y){if(!U){y=p+(y.length>0?" "+y:y);continue}if(Y=r(z),!Y){y=p+(y.length>0?" "+y:y);continue}U=!1}const J=d(w).join(":"),k=C?J+Zc:J,V=k+Y;if(v.includes(V))continue;v.push(V);const X=s(Y,U);for(let I=0;I<X.length;++I){const F=X[I];v.push(k+F)}y=p+(y.length>0?" "+y:y)}return y};function ab(){let a=0,o,c,r="";for(;a<arguments.length;)(o=arguments[a++])&&(c=ov(o))&&(r&&(r+=" "),r+=c);return r}const ov=a=>{if(typeof a=="string")return a;let o,c="";for(let r=0;r<a.length;r++)a[r]&&(o=ov(a[r]))&&(c&&(c+=" "),c+=o);return c};function ub(a,...o){let c,r,s,d=v;function v(y){const h=o.reduce((p,x)=>x(p),a());return c=tb(h),r=c.cache.get,s=c.cache.set,d=g,g(y)}function g(y){const h=r(y);if(h)return h;const p=lb(y,c);return s(y,p),p}return function(){return d(ab.apply(null,arguments))}}const Qe=a=>{const o=c=>c[a]||[];return o.isThemeGetter=!0,o},rv=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,cv=/^\((?:(\w[\w-]*):)?(.+)\)$/i,ib=/^\d+\/\d+$/,ob=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,rb=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,cb=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,sb=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,fb=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,la=a=>ib.test(a),ge=a=>!!a&&!Number.isNaN(Number(a)),Vn=a=>!!a&&Number.isInteger(Number(a)),zc=a=>a.endsWith("%")&&ge(a.slice(0,-1)),hn=a=>ob.test(a),db=()=>!0,mb=a=>rb.test(a)&&!cb.test(a),sv=()=>!1,hb=a=>sb.test(a),vb=a=>fb.test(a),gb=a=>!te(a)&&!ne(a),pb=a=>sa(a,mv,sv),te=a=>rv.test(a),hl=a=>sa(a,hv,mb),Uc=a=>sa(a,Eb,ge),Oh=a=>sa(a,fv,sv),yb=a=>sa(a,dv,vb),qi=a=>sa(a,vv,hb),ne=a=>cv.test(a),mu=a=>fa(a,hv),bb=a=>fa(a,Ab),_h=a=>fa(a,fv),Sb=a=>fa(a,mv),xb=a=>fa(a,dv),ki=a=>fa(a,vv,!0),sa=(a,o,c)=>{const r=rv.exec(a);return r?r[1]?o(r[1]):c(r[2]):!1},fa=(a,o,c=!1)=>{const r=cv.exec(a);return r?r[1]?o(r[1]):c:!1},fv=a=>a==="position"||a==="percentage",dv=a=>a==="image"||a==="url",mv=a=>a==="length"||a==="size"||a==="bg-size",hv=a=>a==="length",Eb=a=>a==="number",Ab=a=>a==="family-name",vv=a=>a==="shadow",wb=()=>{const a=Qe("color"),o=Qe("font"),c=Qe("text"),r=Qe("font-weight"),s=Qe("tracking"),d=Qe("leading"),v=Qe("breakpoint"),g=Qe("container"),y=Qe("spacing"),h=Qe("radius"),p=Qe("shadow"),x=Qe("inset-shadow"),w=Qe("text-shadow"),C=Qe("drop-shadow"),z=Qe("blur"),T=Qe("perspective"),U=Qe("aspect"),Y=Qe("ease"),J=Qe("animate"),k=()=>["auto","avoid","all","avoid-page","page","left","right","column"],V=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],X=()=>[...V(),ne,te],I=()=>["auto","hidden","clip","visible","scroll"],F=()=>["auto","contain","none"],Z=()=>[ne,te,y],re=()=>[la,"full","auto",...Z()],he=()=>[Vn,"none","subgrid",ne,te],Ee=()=>["auto",{span:["full",Vn,ne,te]},Vn,ne,te],se=()=>[Vn,"auto",ne,te],xe=()=>["auto","min","max","fr",ne,te],pe=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],de=()=>["start","end","center","stretch","center-safe","end-safe"],O=()=>["auto",...Z()],Q=()=>[la,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...Z()],j=()=>[a,ne,te],W=()=>[...V(),_h,Oh,{position:[ne,te]}],E=()=>["no-repeat",{repeat:["","x","y","space","round"]}],G=()=>["auto","cover","contain",Sb,pb,{size:[ne,te]}],$=()=>[zc,mu,hl],K=()=>["","none","full",h,ne,te],P=()=>["",ge,mu,hl],fe=()=>["solid","dashed","dotted","double"],ae=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],ue=()=>[ge,zc,_h,Oh],Me=()=>["","none",z,ne,te],nt=()=>["none",ge,ne,te],Lt=()=>["none",ge,ne,te],Gt=()=>[ge,ne,te],Yt=()=>[la,"full",...Z()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[hn],breakpoint:[hn],color:[db],container:[hn],"drop-shadow":[hn],ease:["in","out","in-out"],font:[gb],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[hn],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[hn],shadow:[hn],spacing:["px",ge],text:[hn],"text-shadow":[hn],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",la,te,ne,U]}],container:["container"],columns:[{columns:[ge,te,ne,g]}],"break-after":[{"break-after":k()}],"break-before":[{"break-before":k()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:X()}],overflow:[{overflow:I()}],"overflow-x":[{"overflow-x":I()}],"overflow-y":[{"overflow-y":I()}],overscroll:[{overscroll:F()}],"overscroll-x":[{"overscroll-x":F()}],"overscroll-y":[{"overscroll-y":F()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:re()}],"inset-x":[{"inset-x":re()}],"inset-y":[{"inset-y":re()}],start:[{start:re()}],end:[{end:re()}],top:[{top:re()}],right:[{right:re()}],bottom:[{bottom:re()}],left:[{left:re()}],visibility:["visible","invisible","collapse"],z:[{z:[Vn,"auto",ne,te]}],basis:[{basis:[la,"full","auto",g,...Z()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[ge,la,"auto","initial","none",te]}],grow:[{grow:["",ge,ne,te]}],shrink:[{shrink:["",ge,ne,te]}],order:[{order:[Vn,"first","last","none",ne,te]}],"grid-cols":[{"grid-cols":he()}],"col-start-end":[{col:Ee()}],"col-start":[{"col-start":se()}],"col-end":[{"col-end":se()}],"grid-rows":[{"grid-rows":he()}],"row-start-end":[{row:Ee()}],"row-start":[{"row-start":se()}],"row-end":[{"row-end":se()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":xe()}],"auto-rows":[{"auto-rows":xe()}],gap:[{gap:Z()}],"gap-x":[{"gap-x":Z()}],"gap-y":[{"gap-y":Z()}],"justify-content":[{justify:[...pe(),"normal"]}],"justify-items":[{"justify-items":[...de(),"normal"]}],"justify-self":[{"justify-self":["auto",...de()]}],"align-content":[{content:["normal",...pe()]}],"align-items":[{items:[...de(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...de(),{baseline:["","last"]}]}],"place-content":[{"place-content":pe()}],"place-items":[{"place-items":[...de(),"baseline"]}],"place-self":[{"place-self":["auto",...de()]}],p:[{p:Z()}],px:[{px:Z()}],py:[{py:Z()}],ps:[{ps:Z()}],pe:[{pe:Z()}],pt:[{pt:Z()}],pr:[{pr:Z()}],pb:[{pb:Z()}],pl:[{pl:Z()}],m:[{m:O()}],mx:[{mx:O()}],my:[{my:O()}],ms:[{ms:O()}],me:[{me:O()}],mt:[{mt:O()}],mr:[{mr:O()}],mb:[{mb:O()}],ml:[{ml:O()}],"space-x":[{"space-x":Z()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":Z()}],"space-y-reverse":["space-y-reverse"],size:[{size:Q()}],w:[{w:[g,"screen",...Q()]}],"min-w":[{"min-w":[g,"screen","none",...Q()]}],"max-w":[{"max-w":[g,"screen","none","prose",{screen:[v]},...Q()]}],h:[{h:["screen","lh",...Q()]}],"min-h":[{"min-h":["screen","lh","none",...Q()]}],"max-h":[{"max-h":["screen","lh",...Q()]}],"font-size":[{text:["base",c,mu,hl]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,ne,Uc]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",zc,te]}],"font-family":[{font:[bb,te,o]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[s,ne,te]}],"line-clamp":[{"line-clamp":[ge,"none",ne,Uc]}],leading:[{leading:[d,...Z()]}],"list-image":[{"list-image":["none",ne,te]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",ne,te]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:j()}],"text-color":[{text:j()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...fe(),"wavy"]}],"text-decoration-thickness":[{decoration:[ge,"from-font","auto",ne,hl]}],"text-decoration-color":[{decoration:j()}],"underline-offset":[{"underline-offset":[ge,"auto",ne,te]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:Z()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",ne,te]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",ne,te]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:W()}],"bg-repeat":[{bg:E()}],"bg-size":[{bg:G()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Vn,ne,te],radial:["",ne,te],conic:[Vn,ne,te]},xb,yb]}],"bg-color":[{bg:j()}],"gradient-from-pos":[{from:$()}],"gradient-via-pos":[{via:$()}],"gradient-to-pos":[{to:$()}],"gradient-from":[{from:j()}],"gradient-via":[{via:j()}],"gradient-to":[{to:j()}],rounded:[{rounded:K()}],"rounded-s":[{"rounded-s":K()}],"rounded-e":[{"rounded-e":K()}],"rounded-t":[{"rounded-t":K()}],"rounded-r":[{"rounded-r":K()}],"rounded-b":[{"rounded-b":K()}],"rounded-l":[{"rounded-l":K()}],"rounded-ss":[{"rounded-ss":K()}],"rounded-se":[{"rounded-se":K()}],"rounded-ee":[{"rounded-ee":K()}],"rounded-es":[{"rounded-es":K()}],"rounded-tl":[{"rounded-tl":K()}],"rounded-tr":[{"rounded-tr":K()}],"rounded-br":[{"rounded-br":K()}],"rounded-bl":[{"rounded-bl":K()}],"border-w":[{border:P()}],"border-w-x":[{"border-x":P()}],"border-w-y":[{"border-y":P()}],"border-w-s":[{"border-s":P()}],"border-w-e":[{"border-e":P()}],"border-w-t":[{"border-t":P()}],"border-w-r":[{"border-r":P()}],"border-w-b":[{"border-b":P()}],"border-w-l":[{"border-l":P()}],"divide-x":[{"divide-x":P()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":P()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...fe(),"hidden","none"]}],"divide-style":[{divide:[...fe(),"hidden","none"]}],"border-color":[{border:j()}],"border-color-x":[{"border-x":j()}],"border-color-y":[{"border-y":j()}],"border-color-s":[{"border-s":j()}],"border-color-e":[{"border-e":j()}],"border-color-t":[{"border-t":j()}],"border-color-r":[{"border-r":j()}],"border-color-b":[{"border-b":j()}],"border-color-l":[{"border-l":j()}],"divide-color":[{divide:j()}],"outline-style":[{outline:[...fe(),"none","hidden"]}],"outline-offset":[{"outline-offset":[ge,ne,te]}],"outline-w":[{outline:["",ge,mu,hl]}],"outline-color":[{outline:j()}],shadow:[{shadow:["","none",p,ki,qi]}],"shadow-color":[{shadow:j()}],"inset-shadow":[{"inset-shadow":["none",x,ki,qi]}],"inset-shadow-color":[{"inset-shadow":j()}],"ring-w":[{ring:P()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:j()}],"ring-offset-w":[{"ring-offset":[ge,hl]}],"ring-offset-color":[{"ring-offset":j()}],"inset-ring-w":[{"inset-ring":P()}],"inset-ring-color":[{"inset-ring":j()}],"text-shadow":[{"text-shadow":["none",w,ki,qi]}],"text-shadow-color":[{"text-shadow":j()}],opacity:[{opacity:[ge,ne,te]}],"mix-blend":[{"mix-blend":[...ae(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ae()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[ge]}],"mask-image-linear-from-pos":[{"mask-linear-from":ue()}],"mask-image-linear-to-pos":[{"mask-linear-to":ue()}],"mask-image-linear-from-color":[{"mask-linear-from":j()}],"mask-image-linear-to-color":[{"mask-linear-to":j()}],"mask-image-t-from-pos":[{"mask-t-from":ue()}],"mask-image-t-to-pos":[{"mask-t-to":ue()}],"mask-image-t-from-color":[{"mask-t-from":j()}],"mask-image-t-to-color":[{"mask-t-to":j()}],"mask-image-r-from-pos":[{"mask-r-from":ue()}],"mask-image-r-to-pos":[{"mask-r-to":ue()}],"mask-image-r-from-color":[{"mask-r-from":j()}],"mask-image-r-to-color":[{"mask-r-to":j()}],"mask-image-b-from-pos":[{"mask-b-from":ue()}],"mask-image-b-to-pos":[{"mask-b-to":ue()}],"mask-image-b-from-color":[{"mask-b-from":j()}],"mask-image-b-to-color":[{"mask-b-to":j()}],"mask-image-l-from-pos":[{"mask-l-from":ue()}],"mask-image-l-to-pos":[{"mask-l-to":ue()}],"mask-image-l-from-color":[{"mask-l-from":j()}],"mask-image-l-to-color":[{"mask-l-to":j()}],"mask-image-x-from-pos":[{"mask-x-from":ue()}],"mask-image-x-to-pos":[{"mask-x-to":ue()}],"mask-image-x-from-color":[{"mask-x-from":j()}],"mask-image-x-to-color":[{"mask-x-to":j()}],"mask-image-y-from-pos":[{"mask-y-from":ue()}],"mask-image-y-to-pos":[{"mask-y-to":ue()}],"mask-image-y-from-color":[{"mask-y-from":j()}],"mask-image-y-to-color":[{"mask-y-to":j()}],"mask-image-radial":[{"mask-radial":[ne,te]}],"mask-image-radial-from-pos":[{"mask-radial-from":ue()}],"mask-image-radial-to-pos":[{"mask-radial-to":ue()}],"mask-image-radial-from-color":[{"mask-radial-from":j()}],"mask-image-radial-to-color":[{"mask-radial-to":j()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":V()}],"mask-image-conic-pos":[{"mask-conic":[ge]}],"mask-image-conic-from-pos":[{"mask-conic-from":ue()}],"mask-image-conic-to-pos":[{"mask-conic-to":ue()}],"mask-image-conic-from-color":[{"mask-conic-from":j()}],"mask-image-conic-to-color":[{"mask-conic-to":j()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:W()}],"mask-repeat":[{mask:E()}],"mask-size":[{mask:G()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",ne,te]}],filter:[{filter:["","none",ne,te]}],blur:[{blur:Me()}],brightness:[{brightness:[ge,ne,te]}],contrast:[{contrast:[ge,ne,te]}],"drop-shadow":[{"drop-shadow":["","none",C,ki,qi]}],"drop-shadow-color":[{"drop-shadow":j()}],grayscale:[{grayscale:["",ge,ne,te]}],"hue-rotate":[{"hue-rotate":[ge,ne,te]}],invert:[{invert:["",ge,ne,te]}],saturate:[{saturate:[ge,ne,te]}],sepia:[{sepia:["",ge,ne,te]}],"backdrop-filter":[{"backdrop-filter":["","none",ne,te]}],"backdrop-blur":[{"backdrop-blur":Me()}],"backdrop-brightness":[{"backdrop-brightness":[ge,ne,te]}],"backdrop-contrast":[{"backdrop-contrast":[ge,ne,te]}],"backdrop-grayscale":[{"backdrop-grayscale":["",ge,ne,te]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[ge,ne,te]}],"backdrop-invert":[{"backdrop-invert":["",ge,ne,te]}],"backdrop-opacity":[{"backdrop-opacity":[ge,ne,te]}],"backdrop-saturate":[{"backdrop-saturate":[ge,ne,te]}],"backdrop-sepia":[{"backdrop-sepia":["",ge,ne,te]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":Z()}],"border-spacing-x":[{"border-spacing-x":Z()}],"border-spacing-y":[{"border-spacing-y":Z()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",ne,te]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[ge,"initial",ne,te]}],ease:[{ease:["linear","initial",Y,ne,te]}],delay:[{delay:[ge,ne,te]}],animate:[{animate:["none",J,ne,te]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[T,ne,te]}],"perspective-origin":[{"perspective-origin":X()}],rotate:[{rotate:nt()}],"rotate-x":[{"rotate-x":nt()}],"rotate-y":[{"rotate-y":nt()}],"rotate-z":[{"rotate-z":nt()}],scale:[{scale:Lt()}],"scale-x":[{"scale-x":Lt()}],"scale-y":[{"scale-y":Lt()}],"scale-z":[{"scale-z":Lt()}],"scale-3d":["scale-3d"],skew:[{skew:Gt()}],"skew-x":[{"skew-x":Gt()}],"skew-y":[{"skew-y":Gt()}],transform:[{transform:[ne,te,"","none","gpu","cpu"]}],"transform-origin":[{origin:X()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:Yt()}],"translate-x":[{"translate-x":Yt()}],"translate-y":[{"translate-y":Yt()}],"translate-z":[{"translate-z":Yt()}],"translate-none":["translate-none"],accent:[{accent:j()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:j()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",ne,te]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":Z()}],"scroll-mx":[{"scroll-mx":Z()}],"scroll-my":[{"scroll-my":Z()}],"scroll-ms":[{"scroll-ms":Z()}],"scroll-me":[{"scroll-me":Z()}],"scroll-mt":[{"scroll-mt":Z()}],"scroll-mr":[{"scroll-mr":Z()}],"scroll-mb":[{"scroll-mb":Z()}],"scroll-ml":[{"scroll-ml":Z()}],"scroll-p":[{"scroll-p":Z()}],"scroll-px":[{"scroll-px":Z()}],"scroll-py":[{"scroll-py":Z()}],"scroll-ps":[{"scroll-ps":Z()}],"scroll-pe":[{"scroll-pe":Z()}],"scroll-pt":[{"scroll-pt":Z()}],"scroll-pr":[{"scroll-pr":Z()}],"scroll-pb":[{"scroll-pb":Z()}],"scroll-pl":[{"scroll-pl":Z()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",ne,te]}],fill:[{fill:["none",...j()]}],"stroke-w":[{stroke:[ge,mu,hl,Uc]}],stroke:[{stroke:["none",...j()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},Mb=ub(wb);function yn(...a){return Mb(uv(a))}const Tb=Q0("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-white hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function gv({className:a,variant:o,size:c,asChild:r=!1,...s}){const d=r?G0:"button";return H.jsx(d,{"data-slot":"button",className:yn(Tb({variant:o,size:c,className:a})),...s})}const Rb={theme:"system",setTheme:()=>null},pv=b.createContext(Rb);function Ob({children:a,defaultTheme:o="system",storageKey:c="vite-ui-theme",...r}){const[s,d]=b.useState(()=>localStorage.getItem(c)||o);b.useEffect(()=>{const g=window.document.documentElement;if(g.classList.remove("light","dark"),s==="system"){const y=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";g.classList.add(y);return}g.classList.add(s)},[s]);const v={theme:s,setTheme:g=>{localStorage.setItem(c,g),d(g)}};return H.jsx(pv.Provider,{...r,value:v,children:a})}const _b=()=>{const a=b.useContext(pv);if(a===void 0)throw new Error("useTheme must be used within a ThemeProvider");return a};/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cb=a=>a.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Db=a=>a.replace(/^([A-Z])|[\s-_]+(\w)/g,(o,c,r)=>r?r.toUpperCase():c.toLowerCase()),Ch=a=>{const o=Db(a);return o.charAt(0).toUpperCase()+o.slice(1)},yv=(...a)=>a.filter((o,c,r)=>!!o&&o.trim()!==""&&r.indexOf(o)===c).join(" ").trim(),Nb=a=>{for(const o in a)if(o.startsWith("aria-")||o==="role"||o==="title")return!0};/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var zb={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ub=b.forwardRef(({color:a="currentColor",size:o=24,strokeWidth:c=2,absoluteStrokeWidth:r,className:s="",children:d,iconNode:v,...g},y)=>b.createElement("svg",{ref:y,...zb,width:o,height:o,stroke:a,strokeWidth:r?Number(c)*24/Number(o):c,className:yv("lucide",s),...!d&&!Nb(g)&&{"aria-hidden":"true"},...g},[...v.map(([h,p])=>b.createElement(h,p)),...Array.isArray(d)?d:[d]]));/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Su=(a,o)=>{const c=b.forwardRef(({className:r,...s},d)=>b.createElement(Ub,{ref:d,iconNode:o,className:yv(`lucide-${Cb(Ch(a))}`,`lucide-${a}`,r),...s}));return c.displayName=Ch(a),c};/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jb=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],Hb=Su("check",jb);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bb=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],Lb=Su("chevron-right",Bb);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gb=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],Yb=Su("circle",Gb);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qb=[["path",{d:"M20.985 12.486a9 9 0 1 1-9.473-9.472c.405-.022.617.46.402.803a6 6 0 0 0 8.268 8.268c.344-.215.825-.004.803.401",key:"kfwtm"}]],kb=Su("moon",qb);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xb=[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]],Vb=Su("sun",Xb);function we(a,o,{checkForDefaultPrevented:c=!0}={}){return function(s){if(a?.(s),c===!1||!s.defaultPrevented)return o?.(s)}}function xu(a,o=[]){let c=[];function r(d,v){const g=b.createContext(v),y=c.length;c=[...c,v];const h=x=>{const{scope:w,children:C,...z}=x,T=w?.[a]?.[y]||g,U=b.useMemo(()=>z,Object.values(z));return H.jsx(T.Provider,{value:U,children:C})};h.displayName=d+"Provider";function p(x,w){const C=w?.[a]?.[y]||g,z=b.useContext(C);if(z)return z;if(v!==void 0)return v;throw new Error(`\`${x}\` must be used within \`${d}\``)}return[h,p]}const s=()=>{const d=c.map(v=>b.createContext(v));return function(g){const y=g?.[a]||d;return b.useMemo(()=>({[`__scope${a}`]:{...g,[a]:y}}),[g,y])}};return s.scopeName=a,[r,Qb(s,...o)]}function Qb(...a){const o=a[0];if(a.length===1)return o;const c=()=>{const r=a.map(s=>({useScope:s(),scopeName:s.scopeName}));return function(d){const v=r.reduce((g,{useScope:y,scopeName:h})=>{const x=y(d)[`__scope${h}`];return{...g,...x}},{});return b.useMemo(()=>({[`__scope${o.scopeName}`]:v}),[v])}};return c.scopeName=o.scopeName,c}var Kn=globalThis?.document?b.useLayoutEffect:()=>{},Zb=nv[" useInsertionEffect ".trim().toString()]||Kn;function bv({prop:a,defaultProp:o,onChange:c=()=>{},caller:r}){const[s,d,v]=Kb({defaultProp:o,onChange:c}),g=a!==void 0,y=g?a:s;{const p=b.useRef(a!==void 0);b.useEffect(()=>{const x=p.current;x!==g&&console.warn(`${r} is changing from ${x?"controlled":"uncontrolled"} to ${g?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),p.current=g},[g,r])}const h=b.useCallback(p=>{if(g){const x=Jb(p)?p(a):p;x!==a&&v.current?.(x)}else d(p)},[g,a,d,v]);return[y,h]}function Kb({defaultProp:a,onChange:o}){const[c,r]=b.useState(a),s=b.useRef(c),d=b.useRef(o);return Zb(()=>{d.current=o},[o]),b.useEffect(()=>{s.current!==c&&(d.current?.(c),s.current=c)},[c,s]),[c,r,d]}function Jb(a){return typeof a=="function"}var as=lv();const Wb=tv(as);var Fb=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],at=Fb.reduce((a,o)=>{const c=vu(`Primitive.${o}`),r=b.forwardRef((s,d)=>{const{asChild:v,...g}=s,y=v?c:o;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),H.jsx(y,{...g,ref:d})});return r.displayName=`Primitive.${o}`,{...a,[o]:r}},{});function Sv(a,o){a&&as.flushSync(()=>a.dispatchEvent(o))}function xv(a){const o=a+"CollectionProvider",[c,r]=xu(o),[s,d]=c(o,{collectionRef:{current:null},itemMap:new Map}),v=T=>{const{scope:U,children:Y}=T,J=Qn.useRef(null),k=Qn.useRef(new Map).current;return H.jsx(s,{scope:U,itemMap:k,collectionRef:J,children:Y})};v.displayName=o;const g=a+"CollectionSlot",y=vu(g),h=Qn.forwardRef((T,U)=>{const{scope:Y,children:J}=T,k=d(g,Y),V=ft(U,k.collectionRef);return H.jsx(y,{ref:V,children:J})});h.displayName=g;const p=a+"CollectionItemSlot",x="data-radix-collection-item",w=vu(p),C=Qn.forwardRef((T,U)=>{const{scope:Y,children:J,...k}=T,V=Qn.useRef(null),X=ft(U,V),I=d(p,Y);return Qn.useEffect(()=>(I.itemMap.set(V,{ref:V,...k}),()=>void I.itemMap.delete(V))),H.jsx(w,{[x]:"",ref:X,children:J})});C.displayName=p;function z(T){const U=d(a+"CollectionConsumer",T);return Qn.useCallback(()=>{const J=U.collectionRef.current;if(!J)return[];const k=Array.from(J.querySelectorAll(`[${x}]`));return Array.from(U.itemMap.values()).sort((I,F)=>k.indexOf(I.ref.current)-k.indexOf(F.ref.current))},[U.collectionRef,U.itemMap])}return[{Provider:v,Slot:h,ItemSlot:C},z,r]}var $b=b.createContext(void 0);function Ev(a){const o=b.useContext($b);return a||o||"ltr"}function vn(a){const o=b.useRef(a);return b.useEffect(()=>{o.current=a}),b.useMemo(()=>(...c)=>o.current?.(...c),[])}function Pb(a,o=globalThis?.document){const c=vn(a);b.useEffect(()=>{const r=s=>{s.key==="Escape"&&c(s)};return o.addEventListener("keydown",r,{capture:!0}),()=>o.removeEventListener("keydown",r,{capture:!0})},[c,o])}var Ib="DismissableLayer",Jc="dismissableLayer.update",e1="dismissableLayer.pointerDownOutside",t1="dismissableLayer.focusOutside",Dh,Av=b.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),wv=b.forwardRef((a,o)=>{const{disableOutsidePointerEvents:c=!1,onEscapeKeyDown:r,onPointerDownOutside:s,onFocusOutside:d,onInteractOutside:v,onDismiss:g,...y}=a,h=b.useContext(Av),[p,x]=b.useState(null),w=p?.ownerDocument??globalThis?.document,[,C]=b.useState({}),z=ft(o,F=>x(F)),T=Array.from(h.layers),[U]=[...h.layersWithOutsidePointerEventsDisabled].slice(-1),Y=T.indexOf(U),J=p?T.indexOf(p):-1,k=h.layersWithOutsidePointerEventsDisabled.size>0,V=J>=Y,X=a1(F=>{const Z=F.target,re=[...h.branches].some(he=>he.contains(Z));!V||re||(s?.(F),v?.(F),F.defaultPrevented||g?.())},w),I=u1(F=>{const Z=F.target;[...h.branches].some(he=>he.contains(Z))||(d?.(F),v?.(F),F.defaultPrevented||g?.())},w);return Pb(F=>{J===h.layers.size-1&&(r?.(F),!F.defaultPrevented&&g&&(F.preventDefault(),g()))},w),b.useEffect(()=>{if(p)return c&&(h.layersWithOutsidePointerEventsDisabled.size===0&&(Dh=w.body.style.pointerEvents,w.body.style.pointerEvents="none"),h.layersWithOutsidePointerEventsDisabled.add(p)),h.layers.add(p),Nh(),()=>{c&&h.layersWithOutsidePointerEventsDisabled.size===1&&(w.body.style.pointerEvents=Dh)}},[p,w,c,h]),b.useEffect(()=>()=>{p&&(h.layers.delete(p),h.layersWithOutsidePointerEventsDisabled.delete(p),Nh())},[p,h]),b.useEffect(()=>{const F=()=>C({});return document.addEventListener(Jc,F),()=>document.removeEventListener(Jc,F)},[]),H.jsx(at.div,{...y,ref:z,style:{pointerEvents:k?V?"auto":"none":void 0,...a.style},onFocusCapture:we(a.onFocusCapture,I.onFocusCapture),onBlurCapture:we(a.onBlurCapture,I.onBlurCapture),onPointerDownCapture:we(a.onPointerDownCapture,X.onPointerDownCapture)})});wv.displayName=Ib;var n1="DismissableLayerBranch",l1=b.forwardRef((a,o)=>{const c=b.useContext(Av),r=b.useRef(null),s=ft(o,r);return b.useEffect(()=>{const d=r.current;if(d)return c.branches.add(d),()=>{c.branches.delete(d)}},[c.branches]),H.jsx(at.div,{...a,ref:s})});l1.displayName=n1;function a1(a,o=globalThis?.document){const c=vn(a),r=b.useRef(!1),s=b.useRef(()=>{});return b.useEffect(()=>{const d=g=>{if(g.target&&!r.current){let y=function(){Mv(e1,c,h,{discrete:!0})};const h={originalEvent:g};g.pointerType==="touch"?(o.removeEventListener("click",s.current),s.current=y,o.addEventListener("click",s.current,{once:!0})):y()}else o.removeEventListener("click",s.current);r.current=!1},v=window.setTimeout(()=>{o.addEventListener("pointerdown",d)},0);return()=>{window.clearTimeout(v),o.removeEventListener("pointerdown",d),o.removeEventListener("click",s.current)}},[o,c]),{onPointerDownCapture:()=>r.current=!0}}function u1(a,o=globalThis?.document){const c=vn(a),r=b.useRef(!1);return b.useEffect(()=>{const s=d=>{d.target&&!r.current&&Mv(t1,c,{originalEvent:d},{discrete:!1})};return o.addEventListener("focusin",s),()=>o.removeEventListener("focusin",s)},[o,c]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function Nh(){const a=new CustomEvent(Jc);document.dispatchEvent(a)}function Mv(a,o,c,{discrete:r}){const s=c.originalEvent.target,d=new CustomEvent(a,{bubbles:!1,cancelable:!0,detail:c});o&&s.addEventListener(a,o,{once:!0}),r?Sv(s,d):s.dispatchEvent(d)}var jc=0;function i1(){b.useEffect(()=>{const a=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",a[0]??zh()),document.body.insertAdjacentElement("beforeend",a[1]??zh()),jc++,()=>{jc===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(o=>o.remove()),jc--}},[])}function zh(){const a=document.createElement("span");return a.setAttribute("data-radix-focus-guard",""),a.tabIndex=0,a.style.outline="none",a.style.opacity="0",a.style.position="fixed",a.style.pointerEvents="none",a}var Hc="focusScope.autoFocusOnMount",Bc="focusScope.autoFocusOnUnmount",Uh={bubbles:!1,cancelable:!0},o1="FocusScope",Tv=b.forwardRef((a,o)=>{const{loop:c=!1,trapped:r=!1,onMountAutoFocus:s,onUnmountAutoFocus:d,...v}=a,[g,y]=b.useState(null),h=vn(s),p=vn(d),x=b.useRef(null),w=ft(o,T=>y(T)),C=b.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;b.useEffect(()=>{if(r){let T=function(k){if(C.paused||!g)return;const V=k.target;g.contains(V)?x.current=V:Zn(x.current,{select:!0})},U=function(k){if(C.paused||!g)return;const V=k.relatedTarget;V!==null&&(g.contains(V)||Zn(x.current,{select:!0}))},Y=function(k){if(document.activeElement===document.body)for(const X of k)X.removedNodes.length>0&&Zn(g)};document.addEventListener("focusin",T),document.addEventListener("focusout",U);const J=new MutationObserver(Y);return g&&J.observe(g,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",T),document.removeEventListener("focusout",U),J.disconnect()}}},[r,g,C.paused]),b.useEffect(()=>{if(g){Hh.add(C);const T=document.activeElement;if(!g.contains(T)){const Y=new CustomEvent(Hc,Uh);g.addEventListener(Hc,h),g.dispatchEvent(Y),Y.defaultPrevented||(r1(m1(Rv(g)),{select:!0}),document.activeElement===T&&Zn(g))}return()=>{g.removeEventListener(Hc,h),setTimeout(()=>{const Y=new CustomEvent(Bc,Uh);g.addEventListener(Bc,p),g.dispatchEvent(Y),Y.defaultPrevented||Zn(T??document.body,{select:!0}),g.removeEventListener(Bc,p),Hh.remove(C)},0)}}},[g,h,p,C]);const z=b.useCallback(T=>{if(!c&&!r||C.paused)return;const U=T.key==="Tab"&&!T.altKey&&!T.ctrlKey&&!T.metaKey,Y=document.activeElement;if(U&&Y){const J=T.currentTarget,[k,V]=c1(J);k&&V?!T.shiftKey&&Y===V?(T.preventDefault(),c&&Zn(k,{select:!0})):T.shiftKey&&Y===k&&(T.preventDefault(),c&&Zn(V,{select:!0})):Y===J&&T.preventDefault()}},[c,r,C.paused]);return H.jsx(at.div,{tabIndex:-1,...v,ref:w,onKeyDown:z})});Tv.displayName=o1;function r1(a,{select:o=!1}={}){const c=document.activeElement;for(const r of a)if(Zn(r,{select:o}),document.activeElement!==c)return}function c1(a){const o=Rv(a),c=jh(o,a),r=jh(o.reverse(),a);return[c,r]}function Rv(a){const o=[],c=document.createTreeWalker(a,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const s=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||s?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;c.nextNode();)o.push(c.currentNode);return o}function jh(a,o){for(const c of a)if(!s1(c,{upTo:o}))return c}function s1(a,{upTo:o}){if(getComputedStyle(a).visibility==="hidden")return!0;for(;a;){if(o!==void 0&&a===o)return!1;if(getComputedStyle(a).display==="none")return!0;a=a.parentElement}return!1}function f1(a){return a instanceof HTMLInputElement&&"select"in a}function Zn(a,{select:o=!1}={}){if(a&&a.focus){const c=document.activeElement;a.focus({preventScroll:!0}),a!==c&&f1(a)&&o&&a.select()}}var Hh=d1();function d1(){let a=[];return{add(o){const c=a[0];o!==c&&c?.pause(),a=Bh(a,o),a.unshift(o)},remove(o){a=Bh(a,o),a[0]?.resume()}}}function Bh(a,o){const c=[...a],r=c.indexOf(o);return r!==-1&&c.splice(r,1),c}function m1(a){return a.filter(o=>o.tagName!=="A")}var h1=nv[" useId ".trim().toString()]||(()=>{}),v1=0;function Wc(a){const[o,c]=b.useState(h1());return Kn(()=>{c(r=>r??String(v1++))},[a]),o?`radix-${o}`:""}const g1=["top","right","bottom","left"],Jn=Math.min,xt=Math.max,Ii=Math.round,Xi=Math.floor,Wt=a=>({x:a,y:a}),p1={left:"right",right:"left",bottom:"top",top:"bottom"},y1={start:"end",end:"start"};function Fc(a,o,c){return xt(a,Jn(o,c))}function gn(a,o){return typeof a=="function"?a(o):a}function pn(a){return a.split("-")[0]}function da(a){return a.split("-")[1]}function us(a){return a==="x"?"y":"x"}function is(a){return a==="y"?"height":"width"}const b1=new Set(["top","bottom"]);function Jt(a){return b1.has(pn(a))?"y":"x"}function os(a){return us(Jt(a))}function S1(a,o,c){c===void 0&&(c=!1);const r=da(a),s=os(a),d=is(s);let v=s==="x"?r===(c?"end":"start")?"right":"left":r==="start"?"bottom":"top";return o.reference[d]>o.floating[d]&&(v=eo(v)),[v,eo(v)]}function x1(a){const o=eo(a);return[$c(a),o,$c(o)]}function $c(a){return a.replace(/start|end/g,o=>y1[o])}const Lh=["left","right"],Gh=["right","left"],E1=["top","bottom"],A1=["bottom","top"];function w1(a,o,c){switch(a){case"top":case"bottom":return c?o?Gh:Lh:o?Lh:Gh;case"left":case"right":return o?E1:A1;default:return[]}}function M1(a,o,c,r){const s=da(a);let d=w1(pn(a),c==="start",r);return s&&(d=d.map(v=>v+"-"+s),o&&(d=d.concat(d.map($c)))),d}function eo(a){return a.replace(/left|right|bottom|top/g,o=>p1[o])}function T1(a){return{top:0,right:0,bottom:0,left:0,...a}}function Ov(a){return typeof a!="number"?T1(a):{top:a,right:a,bottom:a,left:a}}function to(a){const{x:o,y:c,width:r,height:s}=a;return{width:r,height:s,top:c,left:o,right:o+r,bottom:c+s,x:o,y:c}}function Yh(a,o,c){let{reference:r,floating:s}=a;const d=Jt(o),v=os(o),g=is(v),y=pn(o),h=d==="y",p=r.x+r.width/2-s.width/2,x=r.y+r.height/2-s.height/2,w=r[g]/2-s[g]/2;let C;switch(y){case"top":C={x:p,y:r.y-s.height};break;case"bottom":C={x:p,y:r.y+r.height};break;case"right":C={x:r.x+r.width,y:x};break;case"left":C={x:r.x-s.width,y:x};break;default:C={x:r.x,y:r.y}}switch(da(o)){case"start":C[v]-=w*(c&&h?-1:1);break;case"end":C[v]+=w*(c&&h?-1:1);break}return C}const R1=async(a,o,c)=>{const{placement:r="bottom",strategy:s="absolute",middleware:d=[],platform:v}=c,g=d.filter(Boolean),y=await(v.isRTL==null?void 0:v.isRTL(o));let h=await v.getElementRects({reference:a,floating:o,strategy:s}),{x:p,y:x}=Yh(h,r,y),w=r,C={},z=0;for(let T=0;T<g.length;T++){const{name:U,fn:Y}=g[T],{x:J,y:k,data:V,reset:X}=await Y({x:p,y:x,initialPlacement:r,placement:w,strategy:s,middlewareData:C,rects:h,platform:v,elements:{reference:a,floating:o}});p=J??p,x=k??x,C={...C,[U]:{...C[U],...V}},X&&z<=50&&(z++,typeof X=="object"&&(X.placement&&(w=X.placement),X.rects&&(h=X.rects===!0?await v.getElementRects({reference:a,floating:o,strategy:s}):X.rects),{x:p,y:x}=Yh(h,w,y)),T=-1)}return{x:p,y:x,placement:w,strategy:s,middlewareData:C}};async function gu(a,o){var c;o===void 0&&(o={});const{x:r,y:s,platform:d,rects:v,elements:g,strategy:y}=a,{boundary:h="clippingAncestors",rootBoundary:p="viewport",elementContext:x="floating",altBoundary:w=!1,padding:C=0}=gn(o,a),z=Ov(C),U=g[w?x==="floating"?"reference":"floating":x],Y=to(await d.getClippingRect({element:(c=await(d.isElement==null?void 0:d.isElement(U)))==null||c?U:U.contextElement||await(d.getDocumentElement==null?void 0:d.getDocumentElement(g.floating)),boundary:h,rootBoundary:p,strategy:y})),J=x==="floating"?{x:r,y:s,width:v.floating.width,height:v.floating.height}:v.reference,k=await(d.getOffsetParent==null?void 0:d.getOffsetParent(g.floating)),V=await(d.isElement==null?void 0:d.isElement(k))?await(d.getScale==null?void 0:d.getScale(k))||{x:1,y:1}:{x:1,y:1},X=to(d.convertOffsetParentRelativeRectToViewportRelativeRect?await d.convertOffsetParentRelativeRectToViewportRelativeRect({elements:g,rect:J,offsetParent:k,strategy:y}):J);return{top:(Y.top-X.top+z.top)/V.y,bottom:(X.bottom-Y.bottom+z.bottom)/V.y,left:(Y.left-X.left+z.left)/V.x,right:(X.right-Y.right+z.right)/V.x}}const O1=a=>({name:"arrow",options:a,async fn(o){const{x:c,y:r,placement:s,rects:d,platform:v,elements:g,middlewareData:y}=o,{element:h,padding:p=0}=gn(a,o)||{};if(h==null)return{};const x=Ov(p),w={x:c,y:r},C=os(s),z=is(C),T=await v.getDimensions(h),U=C==="y",Y=U?"top":"left",J=U?"bottom":"right",k=U?"clientHeight":"clientWidth",V=d.reference[z]+d.reference[C]-w[C]-d.floating[z],X=w[C]-d.reference[C],I=await(v.getOffsetParent==null?void 0:v.getOffsetParent(h));let F=I?I[k]:0;(!F||!await(v.isElement==null?void 0:v.isElement(I)))&&(F=g.floating[k]||d.floating[z]);const Z=V/2-X/2,re=F/2-T[z]/2-1,he=Jn(x[Y],re),Ee=Jn(x[J],re),se=he,xe=F-T[z]-Ee,pe=F/2-T[z]/2+Z,de=Fc(se,pe,xe),O=!y.arrow&&da(s)!=null&&pe!==de&&d.reference[z]/2-(pe<se?he:Ee)-T[z]/2<0,Q=O?pe<se?pe-se:pe-xe:0;return{[C]:w[C]+Q,data:{[C]:de,centerOffset:pe-de-Q,...O&&{alignmentOffset:Q}},reset:O}}}),_1=function(a){return a===void 0&&(a={}),{name:"flip",options:a,async fn(o){var c,r;const{placement:s,middlewareData:d,rects:v,initialPlacement:g,platform:y,elements:h}=o,{mainAxis:p=!0,crossAxis:x=!0,fallbackPlacements:w,fallbackStrategy:C="bestFit",fallbackAxisSideDirection:z="none",flipAlignment:T=!0,...U}=gn(a,o);if((c=d.arrow)!=null&&c.alignmentOffset)return{};const Y=pn(s),J=Jt(g),k=pn(g)===g,V=await(y.isRTL==null?void 0:y.isRTL(h.floating)),X=w||(k||!T?[eo(g)]:x1(g)),I=z!=="none";!w&&I&&X.push(...M1(g,T,z,V));const F=[g,...X],Z=await gu(o,U),re=[];let he=((r=d.flip)==null?void 0:r.overflows)||[];if(p&&re.push(Z[Y]),x){const pe=S1(s,v,V);re.push(Z[pe[0]],Z[pe[1]])}if(he=[...he,{placement:s,overflows:re}],!re.every(pe=>pe<=0)){var Ee,se;const pe=(((Ee=d.flip)==null?void 0:Ee.index)||0)+1,de=F[pe];if(de&&(!(x==="alignment"?J!==Jt(de):!1)||he.every(j=>Jt(j.placement)===J?j.overflows[0]>0:!0)))return{data:{index:pe,overflows:he},reset:{placement:de}};let O=(se=he.filter(Q=>Q.overflows[0]<=0).sort((Q,j)=>Q.overflows[1]-j.overflows[1])[0])==null?void 0:se.placement;if(!O)switch(C){case"bestFit":{var xe;const Q=(xe=he.filter(j=>{if(I){const W=Jt(j.placement);return W===J||W==="y"}return!0}).map(j=>[j.placement,j.overflows.filter(W=>W>0).reduce((W,E)=>W+E,0)]).sort((j,W)=>j[1]-W[1])[0])==null?void 0:xe[0];Q&&(O=Q);break}case"initialPlacement":O=g;break}if(s!==O)return{reset:{placement:O}}}return{}}}};function qh(a,o){return{top:a.top-o.height,right:a.right-o.width,bottom:a.bottom-o.height,left:a.left-o.width}}function kh(a){return g1.some(o=>a[o]>=0)}const C1=function(a){return a===void 0&&(a={}),{name:"hide",options:a,async fn(o){const{rects:c}=o,{strategy:r="referenceHidden",...s}=gn(a,o);switch(r){case"referenceHidden":{const d=await gu(o,{...s,elementContext:"reference"}),v=qh(d,c.reference);return{data:{referenceHiddenOffsets:v,referenceHidden:kh(v)}}}case"escaped":{const d=await gu(o,{...s,altBoundary:!0}),v=qh(d,c.floating);return{data:{escapedOffsets:v,escaped:kh(v)}}}default:return{}}}}},_v=new Set(["left","top"]);async function D1(a,o){const{placement:c,platform:r,elements:s}=a,d=await(r.isRTL==null?void 0:r.isRTL(s.floating)),v=pn(c),g=da(c),y=Jt(c)==="y",h=_v.has(v)?-1:1,p=d&&y?-1:1,x=gn(o,a);let{mainAxis:w,crossAxis:C,alignmentAxis:z}=typeof x=="number"?{mainAxis:x,crossAxis:0,alignmentAxis:null}:{mainAxis:x.mainAxis||0,crossAxis:x.crossAxis||0,alignmentAxis:x.alignmentAxis};return g&&typeof z=="number"&&(C=g==="end"?z*-1:z),y?{x:C*p,y:w*h}:{x:w*h,y:C*p}}const N1=function(a){return a===void 0&&(a=0),{name:"offset",options:a,async fn(o){var c,r;const{x:s,y:d,placement:v,middlewareData:g}=o,y=await D1(o,a);return v===((c=g.offset)==null?void 0:c.placement)&&(r=g.arrow)!=null&&r.alignmentOffset?{}:{x:s+y.x,y:d+y.y,data:{...y,placement:v}}}}},z1=function(a){return a===void 0&&(a={}),{name:"shift",options:a,async fn(o){const{x:c,y:r,placement:s}=o,{mainAxis:d=!0,crossAxis:v=!1,limiter:g={fn:U=>{let{x:Y,y:J}=U;return{x:Y,y:J}}},...y}=gn(a,o),h={x:c,y:r},p=await gu(o,y),x=Jt(pn(s)),w=us(x);let C=h[w],z=h[x];if(d){const U=w==="y"?"top":"left",Y=w==="y"?"bottom":"right",J=C+p[U],k=C-p[Y];C=Fc(J,C,k)}if(v){const U=x==="y"?"top":"left",Y=x==="y"?"bottom":"right",J=z+p[U],k=z-p[Y];z=Fc(J,z,k)}const T=g.fn({...o,[w]:C,[x]:z});return{...T,data:{x:T.x-c,y:T.y-r,enabled:{[w]:d,[x]:v}}}}}},U1=function(a){return a===void 0&&(a={}),{options:a,fn(o){const{x:c,y:r,placement:s,rects:d,middlewareData:v}=o,{offset:g=0,mainAxis:y=!0,crossAxis:h=!0}=gn(a,o),p={x:c,y:r},x=Jt(s),w=us(x);let C=p[w],z=p[x];const T=gn(g,o),U=typeof T=="number"?{mainAxis:T,crossAxis:0}:{mainAxis:0,crossAxis:0,...T};if(y){const k=w==="y"?"height":"width",V=d.reference[w]-d.floating[k]+U.mainAxis,X=d.reference[w]+d.reference[k]-U.mainAxis;C<V?C=V:C>X&&(C=X)}if(h){var Y,J;const k=w==="y"?"width":"height",V=_v.has(pn(s)),X=d.reference[x]-d.floating[k]+(V&&((Y=v.offset)==null?void 0:Y[x])||0)+(V?0:U.crossAxis),I=d.reference[x]+d.reference[k]+(V?0:((J=v.offset)==null?void 0:J[x])||0)-(V?U.crossAxis:0);z<X?z=X:z>I&&(z=I)}return{[w]:C,[x]:z}}}},j1=function(a){return a===void 0&&(a={}),{name:"size",options:a,async fn(o){var c,r;const{placement:s,rects:d,platform:v,elements:g}=o,{apply:y=()=>{},...h}=gn(a,o),p=await gu(o,h),x=pn(s),w=da(s),C=Jt(s)==="y",{width:z,height:T}=d.floating;let U,Y;x==="top"||x==="bottom"?(U=x,Y=w===(await(v.isRTL==null?void 0:v.isRTL(g.floating))?"start":"end")?"left":"right"):(Y=x,U=w==="end"?"top":"bottom");const J=T-p.top-p.bottom,k=z-p.left-p.right,V=Jn(T-p[U],J),X=Jn(z-p[Y],k),I=!o.middlewareData.shift;let F=V,Z=X;if((c=o.middlewareData.shift)!=null&&c.enabled.x&&(Z=k),(r=o.middlewareData.shift)!=null&&r.enabled.y&&(F=J),I&&!w){const he=xt(p.left,0),Ee=xt(p.right,0),se=xt(p.top,0),xe=xt(p.bottom,0);C?Z=z-2*(he!==0||Ee!==0?he+Ee:xt(p.left,p.right)):F=T-2*(se!==0||xe!==0?se+xe:xt(p.top,p.bottom))}await y({...o,availableWidth:Z,availableHeight:F});const re=await v.getDimensions(g.floating);return z!==re.width||T!==re.height?{reset:{rects:!0}}:{}}}};function io(){return typeof window<"u"}function ma(a){return Cv(a)?(a.nodeName||"").toLowerCase():"#document"}function Et(a){var o;return(a==null||(o=a.ownerDocument)==null?void 0:o.defaultView)||window}function $t(a){var o;return(o=(Cv(a)?a.ownerDocument:a.document)||window.document)==null?void 0:o.documentElement}function Cv(a){return io()?a instanceof Node||a instanceof Et(a).Node:!1}function Ht(a){return io()?a instanceof Element||a instanceof Et(a).Element:!1}function Ft(a){return io()?a instanceof HTMLElement||a instanceof Et(a).HTMLElement:!1}function Xh(a){return!io()||typeof ShadowRoot>"u"?!1:a instanceof ShadowRoot||a instanceof Et(a).ShadowRoot}const H1=new Set(["inline","contents"]);function Eu(a){const{overflow:o,overflowX:c,overflowY:r,display:s}=Bt(a);return/auto|scroll|overlay|hidden|clip/.test(o+r+c)&&!H1.has(s)}const B1=new Set(["table","td","th"]);function L1(a){return B1.has(ma(a))}const G1=[":popover-open",":modal"];function oo(a){return G1.some(o=>{try{return a.matches(o)}catch{return!1}})}const Y1=["transform","translate","scale","rotate","perspective"],q1=["transform","translate","scale","rotate","perspective","filter"],k1=["paint","layout","strict","content"];function rs(a){const o=cs(),c=Ht(a)?Bt(a):a;return Y1.some(r=>c[r]?c[r]!=="none":!1)||(c.containerType?c.containerType!=="normal":!1)||!o&&(c.backdropFilter?c.backdropFilter!=="none":!1)||!o&&(c.filter?c.filter!=="none":!1)||q1.some(r=>(c.willChange||"").includes(r))||k1.some(r=>(c.contain||"").includes(r))}function X1(a){let o=Wn(a);for(;Ft(o)&&!ca(o);){if(rs(o))return o;if(oo(o))return null;o=Wn(o)}return null}function cs(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const V1=new Set(["html","body","#document"]);function ca(a){return V1.has(ma(a))}function Bt(a){return Et(a).getComputedStyle(a)}function ro(a){return Ht(a)?{scrollLeft:a.scrollLeft,scrollTop:a.scrollTop}:{scrollLeft:a.scrollX,scrollTop:a.scrollY}}function Wn(a){if(ma(a)==="html")return a;const o=a.assignedSlot||a.parentNode||Xh(a)&&a.host||$t(a);return Xh(o)?o.host:o}function Dv(a){const o=Wn(a);return ca(o)?a.ownerDocument?a.ownerDocument.body:a.body:Ft(o)&&Eu(o)?o:Dv(o)}function pu(a,o,c){var r;o===void 0&&(o=[]),c===void 0&&(c=!0);const s=Dv(a),d=s===((r=a.ownerDocument)==null?void 0:r.body),v=Et(s);if(d){const g=Pc(v);return o.concat(v,v.visualViewport||[],Eu(s)?s:[],g&&c?pu(g):[])}return o.concat(s,pu(s,[],c))}function Pc(a){return a.parent&&Object.getPrototypeOf(a.parent)?a.frameElement:null}function Nv(a){const o=Bt(a);let c=parseFloat(o.width)||0,r=parseFloat(o.height)||0;const s=Ft(a),d=s?a.offsetWidth:c,v=s?a.offsetHeight:r,g=Ii(c)!==d||Ii(r)!==v;return g&&(c=d,r=v),{width:c,height:r,$:g}}function ss(a){return Ht(a)?a:a.contextElement}function oa(a){const o=ss(a);if(!Ft(o))return Wt(1);const c=o.getBoundingClientRect(),{width:r,height:s,$:d}=Nv(o);let v=(d?Ii(c.width):c.width)/r,g=(d?Ii(c.height):c.height)/s;return(!v||!Number.isFinite(v))&&(v=1),(!g||!Number.isFinite(g))&&(g=1),{x:v,y:g}}const Q1=Wt(0);function zv(a){const o=Et(a);return!cs()||!o.visualViewport?Q1:{x:o.visualViewport.offsetLeft,y:o.visualViewport.offsetTop}}function Z1(a,o,c){return o===void 0&&(o=!1),!c||o&&c!==Et(a)?!1:o}function vl(a,o,c,r){o===void 0&&(o=!1),c===void 0&&(c=!1);const s=a.getBoundingClientRect(),d=ss(a);let v=Wt(1);o&&(r?Ht(r)&&(v=oa(r)):v=oa(a));const g=Z1(d,c,r)?zv(d):Wt(0);let y=(s.left+g.x)/v.x,h=(s.top+g.y)/v.y,p=s.width/v.x,x=s.height/v.y;if(d){const w=Et(d),C=r&&Ht(r)?Et(r):r;let z=w,T=Pc(z);for(;T&&r&&C!==z;){const U=oa(T),Y=T.getBoundingClientRect(),J=Bt(T),k=Y.left+(T.clientLeft+parseFloat(J.paddingLeft))*U.x,V=Y.top+(T.clientTop+parseFloat(J.paddingTop))*U.y;y*=U.x,h*=U.y,p*=U.x,x*=U.y,y+=k,h+=V,z=Et(T),T=Pc(z)}}return to({width:p,height:x,x:y,y:h})}function co(a,o){const c=ro(a).scrollLeft;return o?o.left+c:vl($t(a)).left+c}function Uv(a,o){const c=a.getBoundingClientRect(),r=c.left+o.scrollLeft-co(a,c),s=c.top+o.scrollTop;return{x:r,y:s}}function K1(a){let{elements:o,rect:c,offsetParent:r,strategy:s}=a;const d=s==="fixed",v=$t(r),g=o?oo(o.floating):!1;if(r===v||g&&d)return c;let y={scrollLeft:0,scrollTop:0},h=Wt(1);const p=Wt(0),x=Ft(r);if((x||!x&&!d)&&((ma(r)!=="body"||Eu(v))&&(y=ro(r)),Ft(r))){const C=vl(r);h=oa(r),p.x=C.x+r.clientLeft,p.y=C.y+r.clientTop}const w=v&&!x&&!d?Uv(v,y):Wt(0);return{width:c.width*h.x,height:c.height*h.y,x:c.x*h.x-y.scrollLeft*h.x+p.x+w.x,y:c.y*h.y-y.scrollTop*h.y+p.y+w.y}}function J1(a){return Array.from(a.getClientRects())}function W1(a){const o=$t(a),c=ro(a),r=a.ownerDocument.body,s=xt(o.scrollWidth,o.clientWidth,r.scrollWidth,r.clientWidth),d=xt(o.scrollHeight,o.clientHeight,r.scrollHeight,r.clientHeight);let v=-c.scrollLeft+co(a);const g=-c.scrollTop;return Bt(r).direction==="rtl"&&(v+=xt(o.clientWidth,r.clientWidth)-s),{width:s,height:d,x:v,y:g}}const Vh=25;function F1(a,o){const c=Et(a),r=$t(a),s=c.visualViewport;let d=r.clientWidth,v=r.clientHeight,g=0,y=0;if(s){d=s.width,v=s.height;const p=cs();(!p||p&&o==="fixed")&&(g=s.offsetLeft,y=s.offsetTop)}const h=co(r);if(h<=0){const p=r.ownerDocument,x=p.body,w=getComputedStyle(x),C=p.compatMode==="CSS1Compat"&&parseFloat(w.marginLeft)+parseFloat(w.marginRight)||0,z=Math.abs(r.clientWidth-x.clientWidth-C);z<=Vh&&(d-=z)}else h<=Vh&&(d+=h);return{width:d,height:v,x:g,y}}const $1=new Set(["absolute","fixed"]);function P1(a,o){const c=vl(a,!0,o==="fixed"),r=c.top+a.clientTop,s=c.left+a.clientLeft,d=Ft(a)?oa(a):Wt(1),v=a.clientWidth*d.x,g=a.clientHeight*d.y,y=s*d.x,h=r*d.y;return{width:v,height:g,x:y,y:h}}function Qh(a,o,c){let r;if(o==="viewport")r=F1(a,c);else if(o==="document")r=W1($t(a));else if(Ht(o))r=P1(o,c);else{const s=zv(a);r={x:o.x-s.x,y:o.y-s.y,width:o.width,height:o.height}}return to(r)}function jv(a,o){const c=Wn(a);return c===o||!Ht(c)||ca(c)?!1:Bt(c).position==="fixed"||jv(c,o)}function I1(a,o){const c=o.get(a);if(c)return c;let r=pu(a,[],!1).filter(g=>Ht(g)&&ma(g)!=="body"),s=null;const d=Bt(a).position==="fixed";let v=d?Wn(a):a;for(;Ht(v)&&!ca(v);){const g=Bt(v),y=rs(v);!y&&g.position==="fixed"&&(s=null),(d?!y&&!s:!y&&g.position==="static"&&!!s&&$1.has(s.position)||Eu(v)&&!y&&jv(a,v))?r=r.filter(p=>p!==v):s=g,v=Wn(v)}return o.set(a,r),r}function eS(a){let{element:o,boundary:c,rootBoundary:r,strategy:s}=a;const v=[...c==="clippingAncestors"?oo(o)?[]:I1(o,this._c):[].concat(c),r],g=v[0],y=v.reduce((h,p)=>{const x=Qh(o,p,s);return h.top=xt(x.top,h.top),h.right=Jn(x.right,h.right),h.bottom=Jn(x.bottom,h.bottom),h.left=xt(x.left,h.left),h},Qh(o,g,s));return{width:y.right-y.left,height:y.bottom-y.top,x:y.left,y:y.top}}function tS(a){const{width:o,height:c}=Nv(a);return{width:o,height:c}}function nS(a,o,c){const r=Ft(o),s=$t(o),d=c==="fixed",v=vl(a,!0,d,o);let g={scrollLeft:0,scrollTop:0};const y=Wt(0);function h(){y.x=co(s)}if(r||!r&&!d)if((ma(o)!=="body"||Eu(s))&&(g=ro(o)),r){const C=vl(o,!0,d,o);y.x=C.x+o.clientLeft,y.y=C.y+o.clientTop}else s&&h();d&&!r&&s&&h();const p=s&&!r&&!d?Uv(s,g):Wt(0),x=v.left+g.scrollLeft-y.x-p.x,w=v.top+g.scrollTop-y.y-p.y;return{x,y:w,width:v.width,height:v.height}}function Lc(a){return Bt(a).position==="static"}function Zh(a,o){if(!Ft(a)||Bt(a).position==="fixed")return null;if(o)return o(a);let c=a.offsetParent;return $t(a)===c&&(c=c.ownerDocument.body),c}function Hv(a,o){const c=Et(a);if(oo(a))return c;if(!Ft(a)){let s=Wn(a);for(;s&&!ca(s);){if(Ht(s)&&!Lc(s))return s;s=Wn(s)}return c}let r=Zh(a,o);for(;r&&L1(r)&&Lc(r);)r=Zh(r,o);return r&&ca(r)&&Lc(r)&&!rs(r)?c:r||X1(a)||c}const lS=async function(a){const o=this.getOffsetParent||Hv,c=this.getDimensions,r=await c(a.floating);return{reference:nS(a.reference,await o(a.floating),a.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function aS(a){return Bt(a).direction==="rtl"}const uS={convertOffsetParentRelativeRectToViewportRelativeRect:K1,getDocumentElement:$t,getClippingRect:eS,getOffsetParent:Hv,getElementRects:lS,getClientRects:J1,getDimensions:tS,getScale:oa,isElement:Ht,isRTL:aS};function Bv(a,o){return a.x===o.x&&a.y===o.y&&a.width===o.width&&a.height===o.height}function iS(a,o){let c=null,r;const s=$t(a);function d(){var g;clearTimeout(r),(g=c)==null||g.disconnect(),c=null}function v(g,y){g===void 0&&(g=!1),y===void 0&&(y=1),d();const h=a.getBoundingClientRect(),{left:p,top:x,width:w,height:C}=h;if(g||o(),!w||!C)return;const z=Xi(x),T=Xi(s.clientWidth-(p+w)),U=Xi(s.clientHeight-(x+C)),Y=Xi(p),k={rootMargin:-z+"px "+-T+"px "+-U+"px "+-Y+"px",threshold:xt(0,Jn(1,y))||1};let V=!0;function X(I){const F=I[0].intersectionRatio;if(F!==y){if(!V)return v();F?v(!1,F):r=setTimeout(()=>{v(!1,1e-7)},1e3)}F===1&&!Bv(h,a.getBoundingClientRect())&&v(),V=!1}try{c=new IntersectionObserver(X,{...k,root:s.ownerDocument})}catch{c=new IntersectionObserver(X,k)}c.observe(a)}return v(!0),d}function oS(a,o,c,r){r===void 0&&(r={});const{ancestorScroll:s=!0,ancestorResize:d=!0,elementResize:v=typeof ResizeObserver=="function",layoutShift:g=typeof IntersectionObserver=="function",animationFrame:y=!1}=r,h=ss(a),p=s||d?[...h?pu(h):[],...pu(o)]:[];p.forEach(Y=>{s&&Y.addEventListener("scroll",c,{passive:!0}),d&&Y.addEventListener("resize",c)});const x=h&&g?iS(h,c):null;let w=-1,C=null;v&&(C=new ResizeObserver(Y=>{let[J]=Y;J&&J.target===h&&C&&(C.unobserve(o),cancelAnimationFrame(w),w=requestAnimationFrame(()=>{var k;(k=C)==null||k.observe(o)})),c()}),h&&!y&&C.observe(h),C.observe(o));let z,T=y?vl(a):null;y&&U();function U(){const Y=vl(a);T&&!Bv(T,Y)&&c(),T=Y,z=requestAnimationFrame(U)}return c(),()=>{var Y;p.forEach(J=>{s&&J.removeEventListener("scroll",c),d&&J.removeEventListener("resize",c)}),x?.(),(Y=C)==null||Y.disconnect(),C=null,y&&cancelAnimationFrame(z)}}const rS=N1,cS=z1,sS=_1,fS=j1,dS=C1,Kh=O1,mS=U1,hS=(a,o,c)=>{const r=new Map,s={platform:uS,...c},d={...s.platform,_c:r};return R1(a,o,{...s,platform:d})};var vS=typeof document<"u",gS=function(){},Wi=vS?b.useLayoutEffect:gS;function no(a,o){if(a===o)return!0;if(typeof a!=typeof o)return!1;if(typeof a=="function"&&a.toString()===o.toString())return!0;let c,r,s;if(a&&o&&typeof a=="object"){if(Array.isArray(a)){if(c=a.length,c!==o.length)return!1;for(r=c;r--!==0;)if(!no(a[r],o[r]))return!1;return!0}if(s=Object.keys(a),c=s.length,c!==Object.keys(o).length)return!1;for(r=c;r--!==0;)if(!{}.hasOwnProperty.call(o,s[r]))return!1;for(r=c;r--!==0;){const d=s[r];if(!(d==="_owner"&&a.$$typeof)&&!no(a[d],o[d]))return!1}return!0}return a!==a&&o!==o}function Lv(a){return typeof window>"u"?1:(a.ownerDocument.defaultView||window).devicePixelRatio||1}function Jh(a,o){const c=Lv(a);return Math.round(o*c)/c}function Gc(a){const o=b.useRef(a);return Wi(()=>{o.current=a}),o}function pS(a){a===void 0&&(a={});const{placement:o="bottom",strategy:c="absolute",middleware:r=[],platform:s,elements:{reference:d,floating:v}={},transform:g=!0,whileElementsMounted:y,open:h}=a,[p,x]=b.useState({x:0,y:0,strategy:c,placement:o,middlewareData:{},isPositioned:!1}),[w,C]=b.useState(r);no(w,r)||C(r);const[z,T]=b.useState(null),[U,Y]=b.useState(null),J=b.useCallback(j=>{j!==I.current&&(I.current=j,T(j))},[]),k=b.useCallback(j=>{j!==F.current&&(F.current=j,Y(j))},[]),V=d||z,X=v||U,I=b.useRef(null),F=b.useRef(null),Z=b.useRef(p),re=y!=null,he=Gc(y),Ee=Gc(s),se=Gc(h),xe=b.useCallback(()=>{if(!I.current||!F.current)return;const j={placement:o,strategy:c,middleware:w};Ee.current&&(j.platform=Ee.current),hS(I.current,F.current,j).then(W=>{const E={...W,isPositioned:se.current!==!1};pe.current&&!no(Z.current,E)&&(Z.current=E,as.flushSync(()=>{x(E)}))})},[w,o,c,Ee,se]);Wi(()=>{h===!1&&Z.current.isPositioned&&(Z.current.isPositioned=!1,x(j=>({...j,isPositioned:!1})))},[h]);const pe=b.useRef(!1);Wi(()=>(pe.current=!0,()=>{pe.current=!1}),[]),Wi(()=>{if(V&&(I.current=V),X&&(F.current=X),V&&X){if(he.current)return he.current(V,X,xe);xe()}},[V,X,xe,he,re]);const de=b.useMemo(()=>({reference:I,floating:F,setReference:J,setFloating:k}),[J,k]),O=b.useMemo(()=>({reference:V,floating:X}),[V,X]),Q=b.useMemo(()=>{const j={position:c,left:0,top:0};if(!O.floating)return j;const W=Jh(O.floating,p.x),E=Jh(O.floating,p.y);return g?{...j,transform:"translate("+W+"px, "+E+"px)",...Lv(O.floating)>=1.5&&{willChange:"transform"}}:{position:c,left:W,top:E}},[c,g,O.floating,p.x,p.y]);return b.useMemo(()=>({...p,update:xe,refs:de,elements:O,floatingStyles:Q}),[p,xe,de,O,Q])}const yS=a=>{function o(c){return{}.hasOwnProperty.call(c,"current")}return{name:"arrow",options:a,fn(c){const{element:r,padding:s}=typeof a=="function"?a(c):a;return r&&o(r)?r.current!=null?Kh({element:r.current,padding:s}).fn(c):{}:r?Kh({element:r,padding:s}).fn(c):{}}}},bS=(a,o)=>({...rS(a),options:[a,o]}),SS=(a,o)=>({...cS(a),options:[a,o]}),xS=(a,o)=>({...mS(a),options:[a,o]}),ES=(a,o)=>({...sS(a),options:[a,o]}),AS=(a,o)=>({...fS(a),options:[a,o]}),wS=(a,o)=>({...dS(a),options:[a,o]}),MS=(a,o)=>({...yS(a),options:[a,o]});var TS="Arrow",Gv=b.forwardRef((a,o)=>{const{children:c,width:r=10,height:s=5,...d}=a;return H.jsx(at.svg,{...d,ref:o,width:r,height:s,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:a.asChild?c:H.jsx("polygon",{points:"0,0 30,0 15,10"})})});Gv.displayName=TS;var RS=Gv;function OS(a){const[o,c]=b.useState(void 0);return Kn(()=>{if(a){c({width:a.offsetWidth,height:a.offsetHeight});const r=new ResizeObserver(s=>{if(!Array.isArray(s)||!s.length)return;const d=s[0];let v,g;if("borderBoxSize"in d){const y=d.borderBoxSize,h=Array.isArray(y)?y[0]:y;v=h.inlineSize,g=h.blockSize}else v=a.offsetWidth,g=a.offsetHeight;c({width:v,height:g})});return r.observe(a,{box:"border-box"}),()=>r.unobserve(a)}else c(void 0)},[a]),o}var fs="Popper",[Yv,qv]=xu(fs),[_S,kv]=Yv(fs),Xv=a=>{const{__scopePopper:o,children:c}=a,[r,s]=b.useState(null);return H.jsx(_S,{scope:o,anchor:r,onAnchorChange:s,children:c})};Xv.displayName=fs;var Vv="PopperAnchor",Qv=b.forwardRef((a,o)=>{const{__scopePopper:c,virtualRef:r,...s}=a,d=kv(Vv,c),v=b.useRef(null),g=ft(o,v),y=b.useRef(null);return b.useEffect(()=>{const h=y.current;y.current=r?.current||v.current,h!==y.current&&d.onAnchorChange(y.current)}),r?null:H.jsx(at.div,{...s,ref:g})});Qv.displayName=Vv;var ds="PopperContent",[CS,DS]=Yv(ds),Zv=b.forwardRef((a,o)=>{const{__scopePopper:c,side:r="bottom",sideOffset:s=0,align:d="center",alignOffset:v=0,arrowPadding:g=0,avoidCollisions:y=!0,collisionBoundary:h=[],collisionPadding:p=0,sticky:x="partial",hideWhenDetached:w=!1,updatePositionStrategy:C="optimized",onPlaced:z,...T}=a,U=kv(ds,c),[Y,J]=b.useState(null),k=ft(o,ue=>J(ue)),[V,X]=b.useState(null),I=OS(V),F=I?.width??0,Z=I?.height??0,re=r+(d!=="center"?"-"+d:""),he=typeof p=="number"?p:{top:0,right:0,bottom:0,left:0,...p},Ee=Array.isArray(h)?h:[h],se=Ee.length>0,xe={padding:he,boundary:Ee.filter(zS),altBoundary:se},{refs:pe,floatingStyles:de,placement:O,isPositioned:Q,middlewareData:j}=pS({strategy:"fixed",placement:re,whileElementsMounted:(...ue)=>oS(...ue,{animationFrame:C==="always"}),elements:{reference:U.anchor},middleware:[bS({mainAxis:s+Z,alignmentAxis:v}),y&&SS({mainAxis:!0,crossAxis:!1,limiter:x==="partial"?xS():void 0,...xe}),y&&ES({...xe}),AS({...xe,apply:({elements:ue,rects:Me,availableWidth:nt,availableHeight:Lt})=>{const{width:Gt,height:Yt}=Me.reference,bn=ue.floating.style;bn.setProperty("--radix-popper-available-width",`${nt}px`),bn.setProperty("--radix-popper-available-height",`${Lt}px`),bn.setProperty("--radix-popper-anchor-width",`${Gt}px`),bn.setProperty("--radix-popper-anchor-height",`${Yt}px`)}}),V&&MS({element:V,padding:g}),US({arrowWidth:F,arrowHeight:Z}),w&&wS({strategy:"referenceHidden",...xe})]}),[W,E]=Wv(O),G=vn(z);Kn(()=>{Q&&G?.()},[Q,G]);const $=j.arrow?.x,K=j.arrow?.y,P=j.arrow?.centerOffset!==0,[fe,ae]=b.useState();return Kn(()=>{Y&&ae(window.getComputedStyle(Y).zIndex)},[Y]),H.jsx("div",{ref:pe.setFloating,"data-radix-popper-content-wrapper":"",style:{...de,transform:Q?de.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:fe,"--radix-popper-transform-origin":[j.transformOrigin?.x,j.transformOrigin?.y].join(" "),...j.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:a.dir,children:H.jsx(CS,{scope:c,placedSide:W,onArrowChange:X,arrowX:$,arrowY:K,shouldHideArrow:P,children:H.jsx(at.div,{"data-side":W,"data-align":E,...T,ref:k,style:{...T.style,animation:Q?void 0:"none"}})})})});Zv.displayName=ds;var Kv="PopperArrow",NS={top:"bottom",right:"left",bottom:"top",left:"right"},Jv=b.forwardRef(function(o,c){const{__scopePopper:r,...s}=o,d=DS(Kv,r),v=NS[d.placedSide];return H.jsx("span",{ref:d.onArrowChange,style:{position:"absolute",left:d.arrowX,top:d.arrowY,[v]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[d.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[d.placedSide],visibility:d.shouldHideArrow?"hidden":void 0},children:H.jsx(RS,{...s,ref:c,style:{...s.style,display:"block"}})})});Jv.displayName=Kv;function zS(a){return a!==null}var US=a=>({name:"transformOrigin",options:a,fn(o){const{placement:c,rects:r,middlewareData:s}=o,v=s.arrow?.centerOffset!==0,g=v?0:a.arrowWidth,y=v?0:a.arrowHeight,[h,p]=Wv(c),x={start:"0%",center:"50%",end:"100%"}[p],w=(s.arrow?.x??0)+g/2,C=(s.arrow?.y??0)+y/2;let z="",T="";return h==="bottom"?(z=v?x:`${w}px`,T=`${-y}px`):h==="top"?(z=v?x:`${w}px`,T=`${r.floating.height+y}px`):h==="right"?(z=`${-y}px`,T=v?x:`${C}px`):h==="left"&&(z=`${r.floating.width+y}px`,T=v?x:`${C}px`),{data:{x:z,y:T}}}});function Wv(a){const[o,c="center"]=a.split("-");return[o,c]}var jS=Xv,HS=Qv,BS=Zv,LS=Jv,GS="Portal",Fv=b.forwardRef((a,o)=>{const{container:c,...r}=a,[s,d]=b.useState(!1);Kn(()=>d(!0),[]);const v=c||s&&globalThis?.document?.body;return v?Wb.createPortal(H.jsx(at.div,{...r,ref:o}),v):null});Fv.displayName=GS;function YS(a,o){return b.useReducer((c,r)=>o[c][r]??c,a)}var Au=a=>{const{present:o,children:c}=a,r=qS(o),s=typeof c=="function"?c({present:r.isPresent}):b.Children.only(c),d=ft(r.ref,kS(s));return typeof c=="function"||r.isPresent?b.cloneElement(s,{ref:d}):null};Au.displayName="Presence";function qS(a){const[o,c]=b.useState(),r=b.useRef(null),s=b.useRef(a),d=b.useRef("none"),v=a?"mounted":"unmounted",[g,y]=YS(v,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return b.useEffect(()=>{const h=Vi(r.current);d.current=g==="mounted"?h:"none"},[g]),Kn(()=>{const h=r.current,p=s.current;if(p!==a){const w=d.current,C=Vi(h);a?y("MOUNT"):C==="none"||h?.display==="none"?y("UNMOUNT"):y(p&&w!==C?"ANIMATION_OUT":"UNMOUNT"),s.current=a}},[a,y]),Kn(()=>{if(o){let h;const p=o.ownerDocument.defaultView??window,x=C=>{const T=Vi(r.current).includes(CSS.escape(C.animationName));if(C.target===o&&T&&(y("ANIMATION_END"),!s.current)){const U=o.style.animationFillMode;o.style.animationFillMode="forwards",h=p.setTimeout(()=>{o.style.animationFillMode==="forwards"&&(o.style.animationFillMode=U)})}},w=C=>{C.target===o&&(d.current=Vi(r.current))};return o.addEventListener("animationstart",w),o.addEventListener("animationcancel",x),o.addEventListener("animationend",x),()=>{p.clearTimeout(h),o.removeEventListener("animationstart",w),o.removeEventListener("animationcancel",x),o.removeEventListener("animationend",x)}}else y("ANIMATION_END")},[o,y]),{isPresent:["mounted","unmountSuspended"].includes(g),ref:b.useCallback(h=>{r.current=h?getComputedStyle(h):null,c(h)},[])}}function Vi(a){return a?.animationName||"none"}function kS(a){let o=Object.getOwnPropertyDescriptor(a.props,"ref")?.get,c=o&&"isReactWarning"in o&&o.isReactWarning;return c?a.ref:(o=Object.getOwnPropertyDescriptor(a,"ref")?.get,c=o&&"isReactWarning"in o&&o.isReactWarning,c?a.props.ref:a.props.ref||a.ref)}var Yc="rovingFocusGroup.onEntryFocus",XS={bubbles:!1,cancelable:!0},wu="RovingFocusGroup",[Ic,$v,VS]=xv(wu),[QS,Pv]=xu(wu,[VS]),[ZS,KS]=QS(wu),Iv=b.forwardRef((a,o)=>H.jsx(Ic.Provider,{scope:a.__scopeRovingFocusGroup,children:H.jsx(Ic.Slot,{scope:a.__scopeRovingFocusGroup,children:H.jsx(JS,{...a,ref:o})})}));Iv.displayName=wu;var JS=b.forwardRef((a,o)=>{const{__scopeRovingFocusGroup:c,orientation:r,loop:s=!1,dir:d,currentTabStopId:v,defaultCurrentTabStopId:g,onCurrentTabStopIdChange:y,onEntryFocus:h,preventScrollOnEntryFocus:p=!1,...x}=a,w=b.useRef(null),C=ft(o,w),z=Ev(d),[T,U]=bv({prop:v,defaultProp:g??null,onChange:y,caller:wu}),[Y,J]=b.useState(!1),k=vn(h),V=$v(c),X=b.useRef(!1),[I,F]=b.useState(0);return b.useEffect(()=>{const Z=w.current;if(Z)return Z.addEventListener(Yc,k),()=>Z.removeEventListener(Yc,k)},[k]),H.jsx(ZS,{scope:c,orientation:r,dir:z,loop:s,currentTabStopId:T,onItemFocus:b.useCallback(Z=>U(Z),[U]),onItemShiftTab:b.useCallback(()=>J(!0),[]),onFocusableItemAdd:b.useCallback(()=>F(Z=>Z+1),[]),onFocusableItemRemove:b.useCallback(()=>F(Z=>Z-1),[]),children:H.jsx(at.div,{tabIndex:Y||I===0?-1:0,"data-orientation":r,...x,ref:C,style:{outline:"none",...a.style},onMouseDown:we(a.onMouseDown,()=>{X.current=!0}),onFocus:we(a.onFocus,Z=>{const re=!X.current;if(Z.target===Z.currentTarget&&re&&!Y){const he=new CustomEvent(Yc,XS);if(Z.currentTarget.dispatchEvent(he),!he.defaultPrevented){const Ee=V().filter(O=>O.focusable),se=Ee.find(O=>O.active),xe=Ee.find(O=>O.id===T),de=[se,xe,...Ee].filter(Boolean).map(O=>O.ref.current);ng(de,p)}}X.current=!1}),onBlur:we(a.onBlur,()=>J(!1))})})}),eg="RovingFocusGroupItem",tg=b.forwardRef((a,o)=>{const{__scopeRovingFocusGroup:c,focusable:r=!0,active:s=!1,tabStopId:d,children:v,...g}=a,y=Wc(),h=d||y,p=KS(eg,c),x=p.currentTabStopId===h,w=$v(c),{onFocusableItemAdd:C,onFocusableItemRemove:z,currentTabStopId:T}=p;return b.useEffect(()=>{if(r)return C(),()=>z()},[r,C,z]),H.jsx(Ic.ItemSlot,{scope:c,id:h,focusable:r,active:s,children:H.jsx(at.span,{tabIndex:x?0:-1,"data-orientation":p.orientation,...g,ref:o,onMouseDown:we(a.onMouseDown,U=>{r?p.onItemFocus(h):U.preventDefault()}),onFocus:we(a.onFocus,()=>p.onItemFocus(h)),onKeyDown:we(a.onKeyDown,U=>{if(U.key==="Tab"&&U.shiftKey){p.onItemShiftTab();return}if(U.target!==U.currentTarget)return;const Y=$S(U,p.orientation,p.dir);if(Y!==void 0){if(U.metaKey||U.ctrlKey||U.altKey||U.shiftKey)return;U.preventDefault();let k=w().filter(V=>V.focusable).map(V=>V.ref.current);if(Y==="last")k.reverse();else if(Y==="prev"||Y==="next"){Y==="prev"&&k.reverse();const V=k.indexOf(U.currentTarget);k=p.loop?PS(k,V+1):k.slice(V+1)}setTimeout(()=>ng(k))}}),children:typeof v=="function"?v({isCurrentTabStop:x,hasTabStop:T!=null}):v})})});tg.displayName=eg;var WS={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function FS(a,o){return o!=="rtl"?a:a==="ArrowLeft"?"ArrowRight":a==="ArrowRight"?"ArrowLeft":a}function $S(a,o,c){const r=FS(a.key,c);if(!(o==="vertical"&&["ArrowLeft","ArrowRight"].includes(r))&&!(o==="horizontal"&&["ArrowUp","ArrowDown"].includes(r)))return WS[r]}function ng(a,o=!1){const c=document.activeElement;for(const r of a)if(r===c||(r.focus({preventScroll:o}),document.activeElement!==c))return}function PS(a,o){return a.map((c,r)=>a[(o+r)%a.length])}var IS=Iv,ex=tg,tx=function(a){if(typeof document>"u")return null;var o=Array.isArray(a)?a[0]:a;return o.ownerDocument.body},aa=new WeakMap,Qi=new WeakMap,Zi={},qc=0,lg=function(a){return a&&(a.host||lg(a.parentNode))},nx=function(a,o){return o.map(function(c){if(a.contains(c))return c;var r=lg(c);return r&&a.contains(r)?r:(console.error("aria-hidden",c,"in not contained inside",a,". Doing nothing"),null)}).filter(function(c){return!!c})},lx=function(a,o,c,r){var s=nx(o,Array.isArray(a)?a:[a]);Zi[c]||(Zi[c]=new WeakMap);var d=Zi[c],v=[],g=new Set,y=new Set(s),h=function(x){!x||g.has(x)||(g.add(x),h(x.parentNode))};s.forEach(h);var p=function(x){!x||y.has(x)||Array.prototype.forEach.call(x.children,function(w){if(g.has(w))p(w);else try{var C=w.getAttribute(r),z=C!==null&&C!=="false",T=(aa.get(w)||0)+1,U=(d.get(w)||0)+1;aa.set(w,T),d.set(w,U),v.push(w),T===1&&z&&Qi.set(w,!0),U===1&&w.setAttribute(c,"true"),z||w.setAttribute(r,"true")}catch(Y){console.error("aria-hidden: cannot operate on ",w,Y)}})};return p(o),g.clear(),qc++,function(){v.forEach(function(x){var w=aa.get(x)-1,C=d.get(x)-1;aa.set(x,w),d.set(x,C),w||(Qi.has(x)||x.removeAttribute(r),Qi.delete(x)),C||x.removeAttribute(c)}),qc--,qc||(aa=new WeakMap,aa=new WeakMap,Qi=new WeakMap,Zi={})}},ax=function(a,o,c){c===void 0&&(c="data-aria-hidden");var r=Array.from(Array.isArray(a)?a:[a]),s=tx(a);return s?(r.push.apply(r,Array.from(s.querySelectorAll("[aria-live], script"))),lx(r,s,c,"aria-hidden")):function(){return null}},Kt=function(){return Kt=Object.assign||function(o){for(var c,r=1,s=arguments.length;r<s;r++){c=arguments[r];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(o[d]=c[d])}return o},Kt.apply(this,arguments)};function ag(a,o){var c={};for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&o.indexOf(r)<0&&(c[r]=a[r]);if(a!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,r=Object.getOwnPropertySymbols(a);s<r.length;s++)o.indexOf(r[s])<0&&Object.prototype.propertyIsEnumerable.call(a,r[s])&&(c[r[s]]=a[r[s]]);return c}function ux(a,o,c){if(c||arguments.length===2)for(var r=0,s=o.length,d;r<s;r++)(d||!(r in o))&&(d||(d=Array.prototype.slice.call(o,0,r)),d[r]=o[r]);return a.concat(d||Array.prototype.slice.call(o))}var Fi="right-scroll-bar-position",$i="width-before-scroll-bar",ix="with-scroll-bars-hidden",ox="--removed-body-scroll-bar-size";function kc(a,o){return typeof a=="function"?a(o):a&&(a.current=o),a}function rx(a,o){var c=b.useState(function(){return{value:a,callback:o,facade:{get current(){return c.value},set current(r){var s=c.value;s!==r&&(c.value=r,c.callback(r,s))}}}})[0];return c.callback=o,c.facade}var cx=typeof window<"u"?b.useLayoutEffect:b.useEffect,Wh=new WeakMap;function sx(a,o){var c=rx(null,function(r){return a.forEach(function(s){return kc(s,r)})});return cx(function(){var r=Wh.get(c);if(r){var s=new Set(r),d=new Set(a),v=c.current;s.forEach(function(g){d.has(g)||kc(g,null)}),d.forEach(function(g){s.has(g)||kc(g,v)})}Wh.set(c,a)},[a]),c}function fx(a){return a}function dx(a,o){o===void 0&&(o=fx);var c=[],r=!1,s={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return c.length?c[c.length-1]:a},useMedium:function(d){var v=o(d,r);return c.push(v),function(){c=c.filter(function(g){return g!==v})}},assignSyncMedium:function(d){for(r=!0;c.length;){var v=c;c=[],v.forEach(d)}c={push:function(g){return d(g)},filter:function(){return c}}},assignMedium:function(d){r=!0;var v=[];if(c.length){var g=c;c=[],g.forEach(d),v=c}var y=function(){var p=v;v=[],p.forEach(d)},h=function(){return Promise.resolve().then(y)};h(),c={push:function(p){v.push(p),h()},filter:function(p){return v=v.filter(p),c}}}};return s}function mx(a){a===void 0&&(a={});var o=dx(null);return o.options=Kt({async:!0,ssr:!1},a),o}var ug=function(a){var o=a.sideCar,c=ag(a,["sideCar"]);if(!o)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=o.read();if(!r)throw new Error("Sidecar medium not found");return b.createElement(r,Kt({},c))};ug.isSideCarExport=!0;function hx(a,o){return a.useMedium(o),ug}var ig=mx(),Xc=function(){},so=b.forwardRef(function(a,o){var c=b.useRef(null),r=b.useState({onScrollCapture:Xc,onWheelCapture:Xc,onTouchMoveCapture:Xc}),s=r[0],d=r[1],v=a.forwardProps,g=a.children,y=a.className,h=a.removeScrollBar,p=a.enabled,x=a.shards,w=a.sideCar,C=a.noRelative,z=a.noIsolation,T=a.inert,U=a.allowPinchZoom,Y=a.as,J=Y===void 0?"div":Y,k=a.gapMode,V=ag(a,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),X=w,I=sx([c,o]),F=Kt(Kt({},V),s);return b.createElement(b.Fragment,null,p&&b.createElement(X,{sideCar:ig,removeScrollBar:h,shards:x,noRelative:C,noIsolation:z,inert:T,setCallbacks:d,allowPinchZoom:!!U,lockRef:c,gapMode:k}),v?b.cloneElement(b.Children.only(g),Kt(Kt({},F),{ref:I})):b.createElement(J,Kt({},F,{className:y,ref:I}),g))});so.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};so.classNames={fullWidth:$i,zeroRight:Fi};var vx=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function gx(){if(!document)return null;var a=document.createElement("style");a.type="text/css";var o=vx();return o&&a.setAttribute("nonce",o),a}function px(a,o){a.styleSheet?a.styleSheet.cssText=o:a.appendChild(document.createTextNode(o))}function yx(a){var o=document.head||document.getElementsByTagName("head")[0];o.appendChild(a)}var bx=function(){var a=0,o=null;return{add:function(c){a==0&&(o=gx())&&(px(o,c),yx(o)),a++},remove:function(){a--,!a&&o&&(o.parentNode&&o.parentNode.removeChild(o),o=null)}}},Sx=function(){var a=bx();return function(o,c){b.useEffect(function(){return a.add(o),function(){a.remove()}},[o&&c])}},og=function(){var a=Sx(),o=function(c){var r=c.styles,s=c.dynamic;return a(r,s),null};return o},xx={left:0,top:0,right:0,gap:0},Vc=function(a){return parseInt(a||"",10)||0},Ex=function(a){var o=window.getComputedStyle(document.body),c=o[a==="padding"?"paddingLeft":"marginLeft"],r=o[a==="padding"?"paddingTop":"marginTop"],s=o[a==="padding"?"paddingRight":"marginRight"];return[Vc(c),Vc(r),Vc(s)]},Ax=function(a){if(a===void 0&&(a="margin"),typeof window>"u")return xx;var o=Ex(a),c=document.documentElement.clientWidth,r=window.innerWidth;return{left:o[0],top:o[1],right:o[2],gap:Math.max(0,r-c+o[2]-o[0])}},wx=og(),ra="data-scroll-locked",Mx=function(a,o,c,r){var s=a.left,d=a.top,v=a.right,g=a.gap;return c===void 0&&(c="margin"),`
  .`.concat(ix,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(g,"px ").concat(r,`;
  }
  body[`).concat(ra,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([o&&"position: relative ".concat(r,";"),c==="margin"&&`
    padding-left: `.concat(s,`px;
    padding-top: `).concat(d,`px;
    padding-right: `).concat(v,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(g,"px ").concat(r,`;
    `),c==="padding"&&"padding-right: ".concat(g,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(Fi,` {
    right: `).concat(g,"px ").concat(r,`;
  }
  
  .`).concat($i,` {
    margin-right: `).concat(g,"px ").concat(r,`;
  }
  
  .`).concat(Fi," .").concat(Fi,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat($i," .").concat($i,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(ra,`] {
    `).concat(ox,": ").concat(g,`px;
  }
`)},Fh=function(){var a=parseInt(document.body.getAttribute(ra)||"0",10);return isFinite(a)?a:0},Tx=function(){b.useEffect(function(){return document.body.setAttribute(ra,(Fh()+1).toString()),function(){var a=Fh()-1;a<=0?document.body.removeAttribute(ra):document.body.setAttribute(ra,a.toString())}},[])},Rx=function(a){var o=a.noRelative,c=a.noImportant,r=a.gapMode,s=r===void 0?"margin":r;Tx();var d=b.useMemo(function(){return Ax(s)},[s]);return b.createElement(wx,{styles:Mx(d,!o,s,c?"":"!important")})},es=!1;if(typeof window<"u")try{var Ki=Object.defineProperty({},"passive",{get:function(){return es=!0,!0}});window.addEventListener("test",Ki,Ki),window.removeEventListener("test",Ki,Ki)}catch{es=!1}var ua=es?{passive:!1}:!1,Ox=function(a){return a.tagName==="TEXTAREA"},rg=function(a,o){if(!(a instanceof Element))return!1;var c=window.getComputedStyle(a);return c[o]!=="hidden"&&!(c.overflowY===c.overflowX&&!Ox(a)&&c[o]==="visible")},_x=function(a){return rg(a,"overflowY")},Cx=function(a){return rg(a,"overflowX")},$h=function(a,o){var c=o.ownerDocument,r=o;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var s=cg(a,r);if(s){var d=sg(a,r),v=d[1],g=d[2];if(v>g)return!0}r=r.parentNode}while(r&&r!==c.body);return!1},Dx=function(a){var o=a.scrollTop,c=a.scrollHeight,r=a.clientHeight;return[o,c,r]},Nx=function(a){var o=a.scrollLeft,c=a.scrollWidth,r=a.clientWidth;return[o,c,r]},cg=function(a,o){return a==="v"?_x(o):Cx(o)},sg=function(a,o){return a==="v"?Dx(o):Nx(o)},zx=function(a,o){return a==="h"&&o==="rtl"?-1:1},Ux=function(a,o,c,r,s){var d=zx(a,window.getComputedStyle(o).direction),v=d*r,g=c.target,y=o.contains(g),h=!1,p=v>0,x=0,w=0;do{if(!g)break;var C=sg(a,g),z=C[0],T=C[1],U=C[2],Y=T-U-d*z;(z||Y)&&cg(a,g)&&(x+=Y,w+=z);var J=g.parentNode;g=J&&J.nodeType===Node.DOCUMENT_FRAGMENT_NODE?J.host:J}while(!y&&g!==document.body||y&&(o.contains(g)||o===g));return(p&&Math.abs(x)<1||!p&&Math.abs(w)<1)&&(h=!0),h},Ji=function(a){return"changedTouches"in a?[a.changedTouches[0].clientX,a.changedTouches[0].clientY]:[0,0]},Ph=function(a){return[a.deltaX,a.deltaY]},Ih=function(a){return a&&"current"in a?a.current:a},jx=function(a,o){return a[0]===o[0]&&a[1]===o[1]},Hx=function(a){return`
  .block-interactivity-`.concat(a,` {pointer-events: none;}
  .allow-interactivity-`).concat(a,` {pointer-events: all;}
`)},Bx=0,ia=[];function Lx(a){var o=b.useRef([]),c=b.useRef([0,0]),r=b.useRef(),s=b.useState(Bx++)[0],d=b.useState(og)[0],v=b.useRef(a);b.useEffect(function(){v.current=a},[a]),b.useEffect(function(){if(a.inert){document.body.classList.add("block-interactivity-".concat(s));var T=ux([a.lockRef.current],(a.shards||[]).map(Ih),!0).filter(Boolean);return T.forEach(function(U){return U.classList.add("allow-interactivity-".concat(s))}),function(){document.body.classList.remove("block-interactivity-".concat(s)),T.forEach(function(U){return U.classList.remove("allow-interactivity-".concat(s))})}}},[a.inert,a.lockRef.current,a.shards]);var g=b.useCallback(function(T,U){if("touches"in T&&T.touches.length===2||T.type==="wheel"&&T.ctrlKey)return!v.current.allowPinchZoom;var Y=Ji(T),J=c.current,k="deltaX"in T?T.deltaX:J[0]-Y[0],V="deltaY"in T?T.deltaY:J[1]-Y[1],X,I=T.target,F=Math.abs(k)>Math.abs(V)?"h":"v";if("touches"in T&&F==="h"&&I.type==="range")return!1;var Z=$h(F,I);if(!Z)return!0;if(Z?X=F:(X=F==="v"?"h":"v",Z=$h(F,I)),!Z)return!1;if(!r.current&&"changedTouches"in T&&(k||V)&&(r.current=X),!X)return!0;var re=r.current||X;return Ux(re,U,T,re==="h"?k:V)},[]),y=b.useCallback(function(T){var U=T;if(!(!ia.length||ia[ia.length-1]!==d)){var Y="deltaY"in U?Ph(U):Ji(U),J=o.current.filter(function(X){return X.name===U.type&&(X.target===U.target||U.target===X.shadowParent)&&jx(X.delta,Y)})[0];if(J&&J.should){U.cancelable&&U.preventDefault();return}if(!J){var k=(v.current.shards||[]).map(Ih).filter(Boolean).filter(function(X){return X.contains(U.target)}),V=k.length>0?g(U,k[0]):!v.current.noIsolation;V&&U.cancelable&&U.preventDefault()}}},[]),h=b.useCallback(function(T,U,Y,J){var k={name:T,delta:U,target:Y,should:J,shadowParent:Gx(Y)};o.current.push(k),setTimeout(function(){o.current=o.current.filter(function(V){return V!==k})},1)},[]),p=b.useCallback(function(T){c.current=Ji(T),r.current=void 0},[]),x=b.useCallback(function(T){h(T.type,Ph(T),T.target,g(T,a.lockRef.current))},[]),w=b.useCallback(function(T){h(T.type,Ji(T),T.target,g(T,a.lockRef.current))},[]);b.useEffect(function(){return ia.push(d),a.setCallbacks({onScrollCapture:x,onWheelCapture:x,onTouchMoveCapture:w}),document.addEventListener("wheel",y,ua),document.addEventListener("touchmove",y,ua),document.addEventListener("touchstart",p,ua),function(){ia=ia.filter(function(T){return T!==d}),document.removeEventListener("wheel",y,ua),document.removeEventListener("touchmove",y,ua),document.removeEventListener("touchstart",p,ua)}},[]);var C=a.removeScrollBar,z=a.inert;return b.createElement(b.Fragment,null,z?b.createElement(d,{styles:Hx(s)}):null,C?b.createElement(Rx,{noRelative:a.noRelative,gapMode:a.gapMode}):null)}function Gx(a){for(var o=null;a!==null;)a instanceof ShadowRoot&&(o=a.host,a=a.host),a=a.parentNode;return o}const Yx=hx(ig,Lx);var fg=b.forwardRef(function(a,o){return b.createElement(so,Kt({},a,{ref:o,sideCar:Yx}))});fg.classNames=so.classNames;var ts=["Enter"," "],qx=["ArrowDown","PageUp","Home"],dg=["ArrowUp","PageDown","End"],kx=[...qx,...dg],Xx={ltr:[...ts,"ArrowRight"],rtl:[...ts,"ArrowLeft"]},Vx={ltr:["ArrowLeft"],rtl:["ArrowRight"]},Mu="Menu",[yu,Qx,Zx]=xv(Mu),[gl,mg]=xu(Mu,[Zx,qv,Pv]),fo=qv(),hg=Pv(),[Kx,pl]=gl(Mu),[Jx,Tu]=gl(Mu),vg=a=>{const{__scopeMenu:o,open:c=!1,children:r,dir:s,onOpenChange:d,modal:v=!0}=a,g=fo(o),[y,h]=b.useState(null),p=b.useRef(!1),x=vn(d),w=Ev(s);return b.useEffect(()=>{const C=()=>{p.current=!0,document.addEventListener("pointerdown",z,{capture:!0,once:!0}),document.addEventListener("pointermove",z,{capture:!0,once:!0})},z=()=>p.current=!1;return document.addEventListener("keydown",C,{capture:!0}),()=>{document.removeEventListener("keydown",C,{capture:!0}),document.removeEventListener("pointerdown",z,{capture:!0}),document.removeEventListener("pointermove",z,{capture:!0})}},[]),H.jsx(jS,{...g,children:H.jsx(Kx,{scope:o,open:c,onOpenChange:x,content:y,onContentChange:h,children:H.jsx(Jx,{scope:o,onClose:b.useCallback(()=>x(!1),[x]),isUsingKeyboardRef:p,dir:w,modal:v,children:r})})})};vg.displayName=Mu;var Wx="MenuAnchor",ms=b.forwardRef((a,o)=>{const{__scopeMenu:c,...r}=a,s=fo(c);return H.jsx(HS,{...s,...r,ref:o})});ms.displayName=Wx;var hs="MenuPortal",[Fx,gg]=gl(hs,{forceMount:void 0}),pg=a=>{const{__scopeMenu:o,forceMount:c,children:r,container:s}=a,d=pl(hs,o);return H.jsx(Fx,{scope:o,forceMount:c,children:H.jsx(Au,{present:c||d.open,children:H.jsx(Fv,{asChild:!0,container:s,children:r})})})};pg.displayName=hs;var Nt="MenuContent",[$x,vs]=gl(Nt),yg=b.forwardRef((a,o)=>{const c=gg(Nt,a.__scopeMenu),{forceMount:r=c.forceMount,...s}=a,d=pl(Nt,a.__scopeMenu),v=Tu(Nt,a.__scopeMenu);return H.jsx(yu.Provider,{scope:a.__scopeMenu,children:H.jsx(Au,{present:r||d.open,children:H.jsx(yu.Slot,{scope:a.__scopeMenu,children:v.modal?H.jsx(Px,{...s,ref:o}):H.jsx(Ix,{...s,ref:o})})})})}),Px=b.forwardRef((a,o)=>{const c=pl(Nt,a.__scopeMenu),r=b.useRef(null),s=ft(o,r);return b.useEffect(()=>{const d=r.current;if(d)return ax(d)},[]),H.jsx(gs,{...a,ref:s,trapFocus:c.open,disableOutsidePointerEvents:c.open,disableOutsideScroll:!0,onFocusOutside:we(a.onFocusOutside,d=>d.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>c.onOpenChange(!1)})}),Ix=b.forwardRef((a,o)=>{const c=pl(Nt,a.__scopeMenu);return H.jsx(gs,{...a,ref:o,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>c.onOpenChange(!1)})}),eE=vu("MenuContent.ScrollLock"),gs=b.forwardRef((a,o)=>{const{__scopeMenu:c,loop:r=!1,trapFocus:s,onOpenAutoFocus:d,onCloseAutoFocus:v,disableOutsidePointerEvents:g,onEntryFocus:y,onEscapeKeyDown:h,onPointerDownOutside:p,onFocusOutside:x,onInteractOutside:w,onDismiss:C,disableOutsideScroll:z,...T}=a,U=pl(Nt,c),Y=Tu(Nt,c),J=fo(c),k=hg(c),V=Qx(c),[X,I]=b.useState(null),F=b.useRef(null),Z=ft(o,F,U.onContentChange),re=b.useRef(0),he=b.useRef(""),Ee=b.useRef(0),se=b.useRef(null),xe=b.useRef("right"),pe=b.useRef(0),de=z?fg:b.Fragment,O=z?{as:eE,allowPinchZoom:!0}:void 0,Q=W=>{const E=he.current+W,G=V().filter(ue=>!ue.disabled),$=document.activeElement,K=G.find(ue=>ue.ref.current===$)?.textValue,P=G.map(ue=>ue.textValue),fe=dE(P,E,K),ae=G.find(ue=>ue.textValue===fe)?.ref.current;(function ue(Me){he.current=Me,window.clearTimeout(re.current),Me!==""&&(re.current=window.setTimeout(()=>ue(""),1e3))})(E),ae&&setTimeout(()=>ae.focus())};b.useEffect(()=>()=>window.clearTimeout(re.current),[]),i1();const j=b.useCallback(W=>xe.current===se.current?.side&&hE(W,se.current?.area),[]);return H.jsx($x,{scope:c,searchRef:he,onItemEnter:b.useCallback(W=>{j(W)&&W.preventDefault()},[j]),onItemLeave:b.useCallback(W=>{j(W)||(F.current?.focus(),I(null))},[j]),onTriggerLeave:b.useCallback(W=>{j(W)&&W.preventDefault()},[j]),pointerGraceTimerRef:Ee,onPointerGraceIntentChange:b.useCallback(W=>{se.current=W},[]),children:H.jsx(de,{...O,children:H.jsx(Tv,{asChild:!0,trapped:s,onMountAutoFocus:we(d,W=>{W.preventDefault(),F.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:v,children:H.jsx(wv,{asChild:!0,disableOutsidePointerEvents:g,onEscapeKeyDown:h,onPointerDownOutside:p,onFocusOutside:x,onInteractOutside:w,onDismiss:C,children:H.jsx(IS,{asChild:!0,...k,dir:Y.dir,orientation:"vertical",loop:r,currentTabStopId:X,onCurrentTabStopIdChange:I,onEntryFocus:we(y,W=>{Y.isUsingKeyboardRef.current||W.preventDefault()}),preventScrollOnEntryFocus:!0,children:H.jsx(BS,{role:"menu","aria-orientation":"vertical","data-state":Ug(U.open),"data-radix-menu-content":"",dir:Y.dir,...J,...T,ref:Z,style:{outline:"none",...T.style},onKeyDown:we(T.onKeyDown,W=>{const G=W.target.closest("[data-radix-menu-content]")===W.currentTarget,$=W.ctrlKey||W.altKey||W.metaKey,K=W.key.length===1;G&&(W.key==="Tab"&&W.preventDefault(),!$&&K&&Q(W.key));const P=F.current;if(W.target!==P||!kx.includes(W.key))return;W.preventDefault();const ae=V().filter(ue=>!ue.disabled).map(ue=>ue.ref.current);dg.includes(W.key)&&ae.reverse(),sE(ae)}),onBlur:we(a.onBlur,W=>{W.currentTarget.contains(W.target)||(window.clearTimeout(re.current),he.current="")}),onPointerMove:we(a.onPointerMove,bu(W=>{const E=W.target,G=pe.current!==W.clientX;if(W.currentTarget.contains(E)&&G){const $=W.clientX>pe.current?"right":"left";xe.current=$,pe.current=W.clientX}}))})})})})})})});yg.displayName=Nt;var tE="MenuGroup",ps=b.forwardRef((a,o)=>{const{__scopeMenu:c,...r}=a;return H.jsx(at.div,{role:"group",...r,ref:o})});ps.displayName=tE;var nE="MenuLabel",bg=b.forwardRef((a,o)=>{const{__scopeMenu:c,...r}=a;return H.jsx(at.div,{...r,ref:o})});bg.displayName=nE;var lo="MenuItem",ev="menu.itemSelect",mo=b.forwardRef((a,o)=>{const{disabled:c=!1,onSelect:r,...s}=a,d=b.useRef(null),v=Tu(lo,a.__scopeMenu),g=vs(lo,a.__scopeMenu),y=ft(o,d),h=b.useRef(!1),p=()=>{const x=d.current;if(!c&&x){const w=new CustomEvent(ev,{bubbles:!0,cancelable:!0});x.addEventListener(ev,C=>r?.(C),{once:!0}),Sv(x,w),w.defaultPrevented?h.current=!1:v.onClose()}};return H.jsx(Sg,{...s,ref:y,disabled:c,onClick:we(a.onClick,p),onPointerDown:x=>{a.onPointerDown?.(x),h.current=!0},onPointerUp:we(a.onPointerUp,x=>{h.current||x.currentTarget?.click()}),onKeyDown:we(a.onKeyDown,x=>{const w=g.searchRef.current!=="";c||w&&x.key===" "||ts.includes(x.key)&&(x.currentTarget.click(),x.preventDefault())})})});mo.displayName=lo;var Sg=b.forwardRef((a,o)=>{const{__scopeMenu:c,disabled:r=!1,textValue:s,...d}=a,v=vs(lo,c),g=hg(c),y=b.useRef(null),h=ft(o,y),[p,x]=b.useState(!1),[w,C]=b.useState("");return b.useEffect(()=>{const z=y.current;z&&C((z.textContent??"").trim())},[d.children]),H.jsx(yu.ItemSlot,{scope:c,disabled:r,textValue:s??w,children:H.jsx(ex,{asChild:!0,...g,focusable:!r,children:H.jsx(at.div,{role:"menuitem","data-highlighted":p?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...d,ref:h,onPointerMove:we(a.onPointerMove,bu(z=>{r?v.onItemLeave(z):(v.onItemEnter(z),z.defaultPrevented||z.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:we(a.onPointerLeave,bu(z=>v.onItemLeave(z))),onFocus:we(a.onFocus,()=>x(!0)),onBlur:we(a.onBlur,()=>x(!1))})})})}),lE="MenuCheckboxItem",xg=b.forwardRef((a,o)=>{const{checked:c=!1,onCheckedChange:r,...s}=a;return H.jsx(Tg,{scope:a.__scopeMenu,checked:c,children:H.jsx(mo,{role:"menuitemcheckbox","aria-checked":ao(c)?"mixed":c,...s,ref:o,"data-state":bs(c),onSelect:we(s.onSelect,()=>r?.(ao(c)?!0:!c),{checkForDefaultPrevented:!1})})})});xg.displayName=lE;var Eg="MenuRadioGroup",[aE,uE]=gl(Eg,{value:void 0,onValueChange:()=>{}}),Ag=b.forwardRef((a,o)=>{const{value:c,onValueChange:r,...s}=a,d=vn(r);return H.jsx(aE,{scope:a.__scopeMenu,value:c,onValueChange:d,children:H.jsx(ps,{...s,ref:o})})});Ag.displayName=Eg;var wg="MenuRadioItem",Mg=b.forwardRef((a,o)=>{const{value:c,...r}=a,s=uE(wg,a.__scopeMenu),d=c===s.value;return H.jsx(Tg,{scope:a.__scopeMenu,checked:d,children:H.jsx(mo,{role:"menuitemradio","aria-checked":d,...r,ref:o,"data-state":bs(d),onSelect:we(r.onSelect,()=>s.onValueChange?.(c),{checkForDefaultPrevented:!1})})})});Mg.displayName=wg;var ys="MenuItemIndicator",[Tg,iE]=gl(ys,{checked:!1}),Rg=b.forwardRef((a,o)=>{const{__scopeMenu:c,forceMount:r,...s}=a,d=iE(ys,c);return H.jsx(Au,{present:r||ao(d.checked)||d.checked===!0,children:H.jsx(at.span,{...s,ref:o,"data-state":bs(d.checked)})})});Rg.displayName=ys;var oE="MenuSeparator",Og=b.forwardRef((a,o)=>{const{__scopeMenu:c,...r}=a;return H.jsx(at.div,{role:"separator","aria-orientation":"horizontal",...r,ref:o})});Og.displayName=oE;var rE="MenuArrow",_g=b.forwardRef((a,o)=>{const{__scopeMenu:c,...r}=a,s=fo(c);return H.jsx(LS,{...s,...r,ref:o})});_g.displayName=rE;var cE="MenuSub",[dA,Cg]=gl(cE),hu="MenuSubTrigger",Dg=b.forwardRef((a,o)=>{const c=pl(hu,a.__scopeMenu),r=Tu(hu,a.__scopeMenu),s=Cg(hu,a.__scopeMenu),d=vs(hu,a.__scopeMenu),v=b.useRef(null),{pointerGraceTimerRef:g,onPointerGraceIntentChange:y}=d,h={__scopeMenu:a.__scopeMenu},p=b.useCallback(()=>{v.current&&window.clearTimeout(v.current),v.current=null},[]);return b.useEffect(()=>p,[p]),b.useEffect(()=>{const x=g.current;return()=>{window.clearTimeout(x),y(null)}},[g,y]),H.jsx(ms,{asChild:!0,...h,children:H.jsx(Sg,{id:s.triggerId,"aria-haspopup":"menu","aria-expanded":c.open,"aria-controls":s.contentId,"data-state":Ug(c.open),...a,ref:uo(o,s.onTriggerChange),onClick:x=>{a.onClick?.(x),!(a.disabled||x.defaultPrevented)&&(x.currentTarget.focus(),c.open||c.onOpenChange(!0))},onPointerMove:we(a.onPointerMove,bu(x=>{d.onItemEnter(x),!x.defaultPrevented&&!a.disabled&&!c.open&&!v.current&&(d.onPointerGraceIntentChange(null),v.current=window.setTimeout(()=>{c.onOpenChange(!0),p()},100))})),onPointerLeave:we(a.onPointerLeave,bu(x=>{p();const w=c.content?.getBoundingClientRect();if(w){const C=c.content?.dataset.side,z=C==="right",T=z?-5:5,U=w[z?"left":"right"],Y=w[z?"right":"left"];d.onPointerGraceIntentChange({area:[{x:x.clientX+T,y:x.clientY},{x:U,y:w.top},{x:Y,y:w.top},{x:Y,y:w.bottom},{x:U,y:w.bottom}],side:C}),window.clearTimeout(g.current),g.current=window.setTimeout(()=>d.onPointerGraceIntentChange(null),300)}else{if(d.onTriggerLeave(x),x.defaultPrevented)return;d.onPointerGraceIntentChange(null)}})),onKeyDown:we(a.onKeyDown,x=>{const w=d.searchRef.current!=="";a.disabled||w&&x.key===" "||Xx[r.dir].includes(x.key)&&(c.onOpenChange(!0),c.content?.focus(),x.preventDefault())})})})});Dg.displayName=hu;var Ng="MenuSubContent",zg=b.forwardRef((a,o)=>{const c=gg(Nt,a.__scopeMenu),{forceMount:r=c.forceMount,...s}=a,d=pl(Nt,a.__scopeMenu),v=Tu(Nt,a.__scopeMenu),g=Cg(Ng,a.__scopeMenu),y=b.useRef(null),h=ft(o,y);return H.jsx(yu.Provider,{scope:a.__scopeMenu,children:H.jsx(Au,{present:r||d.open,children:H.jsx(yu.Slot,{scope:a.__scopeMenu,children:H.jsx(gs,{id:g.contentId,"aria-labelledby":g.triggerId,...s,ref:h,align:"start",side:v.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:p=>{v.isUsingKeyboardRef.current&&y.current?.focus(),p.preventDefault()},onCloseAutoFocus:p=>p.preventDefault(),onFocusOutside:we(a.onFocusOutside,p=>{p.target!==g.trigger&&d.onOpenChange(!1)}),onEscapeKeyDown:we(a.onEscapeKeyDown,p=>{v.onClose(),p.preventDefault()}),onKeyDown:we(a.onKeyDown,p=>{const x=p.currentTarget.contains(p.target),w=Vx[v.dir].includes(p.key);x&&w&&(d.onOpenChange(!1),g.trigger?.focus(),p.preventDefault())})})})})})});zg.displayName=Ng;function Ug(a){return a?"open":"closed"}function ao(a){return a==="indeterminate"}function bs(a){return ao(a)?"indeterminate":a?"checked":"unchecked"}function sE(a){const o=document.activeElement;for(const c of a)if(c===o||(c.focus(),document.activeElement!==o))return}function fE(a,o){return a.map((c,r)=>a[(o+r)%a.length])}function dE(a,o,c){const s=o.length>1&&Array.from(o).every(h=>h===o[0])?o[0]:o,d=c?a.indexOf(c):-1;let v=fE(a,Math.max(d,0));s.length===1&&(v=v.filter(h=>h!==c));const y=v.find(h=>h.toLowerCase().startsWith(s.toLowerCase()));return y!==c?y:void 0}function mE(a,o){const{x:c,y:r}=a;let s=!1;for(let d=0,v=o.length-1;d<o.length;v=d++){const g=o[d],y=o[v],h=g.x,p=g.y,x=y.x,w=y.y;p>r!=w>r&&c<(x-h)*(r-p)/(w-p)+h&&(s=!s)}return s}function hE(a,o){if(!o)return!1;const c={x:a.clientX,y:a.clientY};return mE(c,o)}function bu(a){return o=>o.pointerType==="mouse"?a(o):void 0}var vE=vg,gE=ms,pE=pg,yE=yg,bE=ps,SE=bg,xE=mo,EE=xg,AE=Ag,wE=Mg,ME=Rg,TE=Og,RE=_g,OE=Dg,_E=zg,ho="DropdownMenu",[CE]=xu(ho,[mg]),ut=mg(),[DE,jg]=CE(ho),Hg=a=>{const{__scopeDropdownMenu:o,children:c,dir:r,open:s,defaultOpen:d,onOpenChange:v,modal:g=!0}=a,y=ut(o),h=b.useRef(null),[p,x]=bv({prop:s,defaultProp:d??!1,onChange:v,caller:ho});return H.jsx(DE,{scope:o,triggerId:Wc(),triggerRef:h,contentId:Wc(),open:p,onOpenChange:x,onOpenToggle:b.useCallback(()=>x(w=>!w),[x]),modal:g,children:H.jsx(vE,{...y,open:p,onOpenChange:x,dir:r,modal:g,children:c})})};Hg.displayName=ho;var Bg="DropdownMenuTrigger",Lg=b.forwardRef((a,o)=>{const{__scopeDropdownMenu:c,disabled:r=!1,...s}=a,d=jg(Bg,c),v=ut(c);return H.jsx(gE,{asChild:!0,...v,children:H.jsx(at.button,{type:"button",id:d.triggerId,"aria-haspopup":"menu","aria-expanded":d.open,"aria-controls":d.open?d.contentId:void 0,"data-state":d.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...s,ref:uo(o,d.triggerRef),onPointerDown:we(a.onPointerDown,g=>{!r&&g.button===0&&g.ctrlKey===!1&&(d.onOpenToggle(),d.open||g.preventDefault())}),onKeyDown:we(a.onKeyDown,g=>{r||(["Enter"," "].includes(g.key)&&d.onOpenToggle(),g.key==="ArrowDown"&&d.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(g.key)&&g.preventDefault())})})})});Lg.displayName=Bg;var NE="DropdownMenuPortal",Gg=a=>{const{__scopeDropdownMenu:o,...c}=a,r=ut(o);return H.jsx(pE,{...r,...c})};Gg.displayName=NE;var Yg="DropdownMenuContent",qg=b.forwardRef((a,o)=>{const{__scopeDropdownMenu:c,...r}=a,s=jg(Yg,c),d=ut(c),v=b.useRef(!1);return H.jsx(yE,{id:s.contentId,"aria-labelledby":s.triggerId,...d,...r,ref:o,onCloseAutoFocus:we(a.onCloseAutoFocus,g=>{v.current||s.triggerRef.current?.focus(),v.current=!1,g.preventDefault()}),onInteractOutside:we(a.onInteractOutside,g=>{const y=g.detail.originalEvent,h=y.button===0&&y.ctrlKey===!0,p=y.button===2||h;(!s.modal||p)&&(v.current=!0)}),style:{...a.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});qg.displayName=Yg;var zE="DropdownMenuGroup",UE=b.forwardRef((a,o)=>{const{__scopeDropdownMenu:c,...r}=a,s=ut(c);return H.jsx(bE,{...s,...r,ref:o})});UE.displayName=zE;var jE="DropdownMenuLabel",kg=b.forwardRef((a,o)=>{const{__scopeDropdownMenu:c,...r}=a,s=ut(c);return H.jsx(SE,{...s,...r,ref:o})});kg.displayName=jE;var HE="DropdownMenuItem",Xg=b.forwardRef((a,o)=>{const{__scopeDropdownMenu:c,...r}=a,s=ut(c);return H.jsx(xE,{...s,...r,ref:o})});Xg.displayName=HE;var BE="DropdownMenuCheckboxItem",Vg=b.forwardRef((a,o)=>{const{__scopeDropdownMenu:c,...r}=a,s=ut(c);return H.jsx(EE,{...s,...r,ref:o})});Vg.displayName=BE;var LE="DropdownMenuRadioGroup",GE=b.forwardRef((a,o)=>{const{__scopeDropdownMenu:c,...r}=a,s=ut(c);return H.jsx(AE,{...s,...r,ref:o})});GE.displayName=LE;var YE="DropdownMenuRadioItem",Qg=b.forwardRef((a,o)=>{const{__scopeDropdownMenu:c,...r}=a,s=ut(c);return H.jsx(wE,{...s,...r,ref:o})});Qg.displayName=YE;var qE="DropdownMenuItemIndicator",Zg=b.forwardRef((a,o)=>{const{__scopeDropdownMenu:c,...r}=a,s=ut(c);return H.jsx(ME,{...s,...r,ref:o})});Zg.displayName=qE;var kE="DropdownMenuSeparator",Kg=b.forwardRef((a,o)=>{const{__scopeDropdownMenu:c,...r}=a,s=ut(c);return H.jsx(TE,{...s,...r,ref:o})});Kg.displayName=kE;var XE="DropdownMenuArrow",VE=b.forwardRef((a,o)=>{const{__scopeDropdownMenu:c,...r}=a,s=ut(c);return H.jsx(RE,{...s,...r,ref:o})});VE.displayName=XE;var QE="DropdownMenuSubTrigger",Jg=b.forwardRef((a,o)=>{const{__scopeDropdownMenu:c,...r}=a,s=ut(c);return H.jsx(OE,{...s,...r,ref:o})});Jg.displayName=QE;var ZE="DropdownMenuSubContent",Wg=b.forwardRef((a,o)=>{const{__scopeDropdownMenu:c,...r}=a,s=ut(c);return H.jsx(_E,{...s,...r,ref:o,style:{...a.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});Wg.displayName=ZE;var KE=Hg,JE=Lg,WE=Gg,Fg=qg,$g=kg,Pg=Xg,Ig=Vg,ep=Qg,tp=Zg,np=Kg,lp=Jg,ap=Wg;const FE=KE,$E=JE,PE=b.forwardRef(({className:a,inset:o,children:c,...r},s)=>H.jsxs(lp,{ref:s,className:yn("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",o&&"pl-8",a),...r,children:[c,H.jsx(Lb,{className:"ml-auto h-4 w-4"})]}));PE.displayName=lp.displayName;const IE=b.forwardRef(({className:a,...o},c)=>H.jsx(ap,{ref:c,className:yn("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...o}));IE.displayName=ap.displayName;const up=b.forwardRef(({className:a,sideOffset:o=4,...c},r)=>H.jsx(WE,{children:H.jsx(Fg,{ref:r,sideOffset:o,className:yn("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...c})}));up.displayName=Fg.displayName;const Pi=b.forwardRef(({className:a,inset:o,...c},r)=>H.jsx(Pg,{ref:r,className:yn("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",o&&"pl-8",a),...c}));Pi.displayName=Pg.displayName;const eA=b.forwardRef(({className:a,children:o,checked:c,...r},s)=>H.jsxs(Ig,{ref:s,className:yn("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),checked:c,...r,children:[H.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:H.jsx(tp,{children:H.jsx(Hb,{className:"h-4 w-4"})})}),o]}));eA.displayName=Ig.displayName;const tA=b.forwardRef(({className:a,children:o,...c},r)=>H.jsxs(ep,{ref:r,className:yn("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...c,children:[H.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:H.jsx(tp,{children:H.jsx(Yb,{className:"h-2 w-2 fill-current"})})}),o]}));tA.displayName=ep.displayName;const nA=b.forwardRef(({className:a,inset:o,...c},r)=>H.jsx($g,{ref:r,className:yn("px-2 py-1.5 text-sm font-semibold",o&&"pl-8",a),...c}));nA.displayName=$g.displayName;const lA=b.forwardRef(({className:a,...o},c)=>H.jsx(np,{ref:c,className:yn("-mx-1 my-1 h-px bg-muted",a),...o}));lA.displayName=np.displayName;function aA(){const{setTheme:a}=_b();return H.jsxs(FE,{children:[H.jsx($E,{asChild:!0,children:H.jsxs(gv,{variant:"outline",size:"icon",children:[H.jsx(Vb,{className:"h-[1.2rem] w-[1.2rem] scale-100 rotate-0 transition-all dark:scale-0 dark:-rotate-90"}),H.jsx(kb,{className:"absolute h-[1.2rem] w-[1.2rem] scale-0 rotate-90 transition-all dark:scale-100 dark:rotate-0"}),H.jsx("span",{className:"sr-only",children:"Toggle theme"})]})}),H.jsxs(up,{align:"end",children:[H.jsx(Pi,{onClick:()=>a("light"),children:"Light"}),H.jsx(Pi,{onClick:()=>a("dark"),children:"Dark"}),H.jsx(Pi,{onClick:()=>a("system"),children:"System"})]})]})}const uA="/logo/Planfuly_Logo.png",ip=b.forwardRef(({className:a,children:o,...c},r)=>H.jsx("h1",{ref:r,className:a,style:{fontFamily:'"Inter", sans-serif',fontSize:"48px",fontWeight:800,lineHeight:1.2},...c,children:o}));ip.displayName="Heading1";const iA=b.forwardRef(({className:a,children:o,...c},r)=>H.jsx("h2",{ref:r,className:a,style:{fontFamily:'"Inter", sans-serif',fontSize:"40px",fontWeight:700,lineHeight:1.2},...c,children:o}));iA.displayName="Heading2";const oA=b.forwardRef(({className:a,children:o,...c},r)=>H.jsx("h3",{ref:r,className:a,style:{fontFamily:'"Inter", sans-serif',fontSize:"30px",fontWeight:700,lineHeight:1.3},...c,children:o}));oA.displayName="Heading3";const rA=b.forwardRef(({className:a,children:o,...c},r)=>H.jsx("h4",{ref:r,className:a,style:{fontFamily:'"Inter", sans-serif',fontSize:"20px",fontWeight:700,lineHeight:1.4},...c,children:o}));rA.displayName="Heading4";const cA=b.forwardRef(({className:a,children:o,...c},r)=>H.jsx("p",{ref:r,className:a,style:{fontFamily:'"Inter", sans-serif',fontSize:"16px",fontWeight:400,lineHeight:1.5},...c,children:o}));cA.displayName="BodyText";const sA=b.forwardRef(({className:a,children:o,...c},r)=>H.jsx("small",{ref:r,className:a,style:{fontFamily:'"Inter", sans-serif',fontSize:"12px",fontWeight:400,lineHeight:1.4},...c,children:o}));sA.displayName="SmallText";function fA(){return H.jsx(Ob,{defaultTheme:"system",storageKey:"planfuly-ui-theme",children:H.jsxs("div",{className:"relative",children:[H.jsx("div",{className:"absolute top-4 right-4",children:H.jsx(aA,{})}),H.jsx("div",{children:H.jsx("img",{src:uA,className:"logo",alt:"Planfuly logo"})}),H.jsx(ip,{children:"Engage Planfuly"}),H.jsx("div",{className:"flex min-h-svh flex-col items-center justify-center",children:H.jsx(gv,{children:"Click me"})}),H.jsx("p",{className:"footer",children:"Planfuly Inc."})]})})}L0.createRoot(document.getElementById("root")).render(H.jsx(b.StrictMode,{children:H.jsx(fA,{})}));
