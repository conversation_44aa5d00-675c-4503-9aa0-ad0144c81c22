{"name": "tw-animate-css", "version": "1.3.8", "description": "TailwindCSS v4.0 compatible replacement for `tailwindcss-animate`.", "keywords": ["accordion", "animate", "animation", "bits-ui", "collapsible", "css", "fades", "opacity", "radix", "reka", "scale", "shadcn", "slide", "spin", "tailwind-animate", "tailwind", "tailwindcss-animate", "tailwindcss", "transform", "transition", "translate", "zoom"], "homepage": "https://github.com/Wombosvideo/tw-animate-css#readme", "bugs": "https://github.com/Wombosvideo/tw-animate-css/issues", "repository": "Wombosvideo/tw-animate-css", "funding": "https://github.com/sponsors/Wombosvideo", "license": "MIT", "author": {"name": "<PERSON>", "url": "https://github.com/Wombosvideo"}, "type": "module", "exports": "./dist/tw-animate.css", "main": "./dist/tw-animate.css", "files": ["dist"], "simple-git-hooks": {"pre-commit": "pnpx lint-staged", "commit-msg": "pnpx commitlint --edit $1"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "lint-staged": {"*": "prettier --write --ignore-unknown", "package.json": "sort-package-json"}, "prettier": {"printWidth": 100}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@types/node": "^24.3.0", "lint-staged": "^16.1.6", "prettier": "^3.6.2", "simple-git-hooks": "^2.13.1", "sort-package-json": "^3.4.0"}, "scripts": {"build": "pnpx @tailwindcss/cli -i ./src/tw-animate.css -o ./dist/tw-animate.css -m && node ./transform.ts ./dist/tw-animate.css", "format": "prettier --write --ignore-unknown ."}}