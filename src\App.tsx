import { ThemeProvider } from "@/components/theme-provider"
import { BrowserRouter as Router, Routes, Route } from "react-router-dom"
import MainPage from "@/pages/MainPage"
import Dashboard from "@/pages/Dashboard"

function App() {
  return (
    <ThemeProvider defaultTheme="system" storageKey="planfuly-ui-theme">
      <Router>
        <Routes>
          <Route path="/" element={<MainPage />} />
          <Route path="/dashboard" element={<Dashboard />} />
        </Routes>
      </Router>
    </ThemeProvider>
  )
}

export default App
