import { forwardRef } from 'react';
import type { HTMLAttributes, ReactNode } from 'react';

interface Heading2Props extends HTMLAttributes<HTMLHeadingElement> {
  children: ReactNode;
}

const Heading2 = forwardRef<HTMLHeadingElement, Heading2Props>(
  ({ className, children, ...props }, ref) => {
    return (
      <h2
        ref={ref}
        className={className}
        style={{
          fontFamily: '"Inter", sans-serif',
          fontSize: '40px',
          fontWeight: 700,
          lineHeight: 1.2,
        }}
        {...props}
      >
        {children}
      </h2>
    );
  }
);

Heading2.displayName = 'Heading2';

export { Heading2 };
