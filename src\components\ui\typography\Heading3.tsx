import { forwardRef } from 'react';
import type { HTMLAttributes, ReactNode } from 'react';

interface Heading3Props extends HTMLAttributes<HTMLHeadingElement> {
  children: ReactNode;
}

const Heading3 = forwardRef<HTMLHeadingElement, Heading3Props>(
  ({ className, children, ...props }, ref) => {
    return (
      <h3
        ref={ref}
        className={className}
        style={{
          fontFamily: '"Inter", sans-serif',
          fontSize: '30px',
          fontWeight: 700,
          lineHeight: 1.3,
        }}
        {...props}
      >
        {children}
      </h3>
    );
  }
);

Heading3.displayName = 'Heading3';

export { Heading3 };
