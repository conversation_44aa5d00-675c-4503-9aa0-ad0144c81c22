import { forwardRef } from 'react';
import type { HTMLAttributes, ReactNode } from 'react';

interface Heading4Props extends HTMLAttributes<HTMLHeadingElement> {
  children: ReactNode;
}

const Heading4 = forwardRef<HTMLHeadingElement, Heading4Props>(
  ({ className, children, ...props }, ref) => {
    return (
      <h4
        ref={ref}
        className={className}
        style={{
          fontFamily: '"Inter", sans-serif',
          fontSize: '20px',
          fontWeight: 700,
          lineHeight: 1.4,
        }}
        {...props}
      >
        {children}
      </h4>
    );
  }
);

Heading4.displayName = 'Heading4';

export { Heading4 };
