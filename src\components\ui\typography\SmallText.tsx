import { forwardRef } from 'react';
import type { HTMLAttributes, ReactNode } from 'react';

interface SmallTextProps extends HTMLAttributes<HTMLElement> {
  children: ReactNode;
}

const SmallText = forwardRef<HTMLElement, SmallTextProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <small
        ref={ref}
        className={className}
        style={{
          fontFamily: '"Inter", sans-serif',
          fontSize: '12px',
          fontWeight: 400,
          lineHeight: 1.4,
        }}
        {...props}
      >
        {children}
      </small>
    );
  }
);

SmallText.displayName = 'SmallText';

export { SmallText };
