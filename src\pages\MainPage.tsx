import { But<PERSON> } from "@/components/ui/button"
import { ModeToggle } from "@/components/mode-toggle"
import planfulyLogo from '/logo/Planfuly_Logo.png'
import { Heading1 } from "@/components/ui/typography"
import { Layout } from "@/components/ui/layout"
import { useNavigate } from "react-router-dom"

function MainPage() {
  const navigate = useNavigate()

  const handleGoToDashboard = () => {
    navigate('/dashboard')
  }

  return (
    <Layout>
      <div className="relative">
        <div className="absolute top-4 right-4">
          <ModeToggle />
        </div>

        <div>
          <img src={planfulyLogo} className="logo" alt="Planfuly logo" />
        </div>
        <Heading1>Engage Planfuly</Heading1>

        <div className="flex flex-col items-center justify-center flex-1">
          <Button onClick={handleGoToDashboard}>Go to Dashboard</Button>
        </div>
      </div>
    </Layout>
  )
}

export default MainPage
