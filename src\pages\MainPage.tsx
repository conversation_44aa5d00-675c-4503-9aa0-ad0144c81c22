import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ModeToggle } from "@/components/mode-toggle"
import planfulyLogo from '/logo/Planfuly_Logo.png'
import { LoginLayout } from "@/components/ui/layout"
import { useNavigate } from "react-router-dom"
import { User, Lock } from "lucide-react"

function MainPage() {
  const navigate = useNavigate()

  const handleLogin = () => {
    navigate('/dashboard')
  }

  return (
    <LoginLayout>
      <div className="relative min-h-screen flex items-center justify-center bg-background">
        <div className="absolute top-4 right-4">
          <ModeToggle />
        </div>

        <Card className="w-full max-w-md mx-4 relative shadow-lg">
          {/* Logo Tab */}
          <div className="absolute -top-6 left-1/2 transform -translate-x-1/2">
            <div className="bg-primary px-6 py-3 rounded-t-lg flex items-center justify-center">
              <img
                src={planfulyLogo}
                className="h-8 w-auto brightness-0 invert"
                alt="Planfuly logo"
              />
            </div>
          </div>

          <CardHeader className="pt-12 pb-6">
            <CardTitle className="text-center sr-only">Login</CardTitle>
          </CardHeader>

          <CardContent className="space-y-6">
            <div className="space-y-4">
              <Input
                type="text"
                placeholder="Username"
                startIcon={<User className="h-4 w-4" />}
                className="h-12"
              />

              <Input
                type="password"
                placeholder="Password"
                startIcon={<Lock className="h-4 w-4" />}
                className="h-12"
              />
            </div>

            <Button
              onClick={handleLogin}
              className="w-full h-12 text-base font-medium"
              size="lg"
            >
              Login
            </Button>
          </CardContent>
        </Card>
      </div>
    </LoginLayout>
  )
}

export default MainPage
